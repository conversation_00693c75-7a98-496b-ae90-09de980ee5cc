indentation: 2

excluded:
  - .build

analyzer_rules:
  - unused_import

disabled_rules:
  - closure_end_indentation
  - literal_expression_end_indentation
  - line_length
  - todo
  - nesting
  - type_body_length
  - file_length
  - large_tuple
  - identifier_name
  - type_name

opt_in_rules:
  #  - array_init
  - closure_spacing
  - collection_alignment
  - contains_over_first_not_nil
  - empty_count
  - empty_string
  - explicit_init
  - explicit_type_interface:
      severity: warning
      included:
      - property
      excluded:
      - local
      - closure
  - fallthrough
  - fatal_error_message
  - first_where
  - force_unwrapping
  - identical_operands
  - joined_default_parameter
  - legacy_random
  - last_where
  - lower_acl_than_parent
  - modifier_order
  - nimble_operator
  - nslocalizedstring_key
  - number_separator
  - object_literal
  - operator_usage_whitespace
  - override_in_extension
  - private_action
  - prohibited_super_call
  - quick_discouraged_call
  - quick_discouraged_focused_test
  - quick_discouraged_pending_test
  - redundant_nil_coalescing
  - redundant_type_annotation
  - single_test_class
  - sorted_first_last
  - sorted_imports
  - static_operator
  - toggle_bool
  - unavailable_function
  - unneeded_parentheses_in_closure_argument
  - untyped_error_in_catch
  - vertical_whitespace_closing_braces
  - vertical_whitespace_opening_braces
  - xct_specific_matcher
  - yoda_condition
