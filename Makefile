# Makefile for CrateNFC project

# Declare phony targets to ensure commands are always executed
.PHONY: all build clean fetch simulate help

# Use the following commands in this order:
# 1. fetch: Run this occasionally to update dependencies before cleanbuilding.
# 2. clean: Run this before building if you encounter issues or want to ensure a fresh build.
# 3. build: Execute this whenever you want to try out code changes.
# 4. simulate: Use this to see the changes in a simulator after building.

# Define the scripts
BUILD_SCRIPT=scripts/build_all.zsh
CLEAN_SCRIPT=scripts/clean_all.zsh
FETCH_SCRIPT=scripts/fetch_dependencies.zsh
SIMULATE_SCRIPT=scripts/simulate_all.zsh

# Default target
all: build

# Build the project for all simulators
build:
	@echo "Building the project for all simulators..."
	@$(BUILD_SCRIPT)

# Build the project for specific simulators using aliases
build_ip:
	@echo "Building the project for iPhone 15 Pro..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone 15 Pro" build

build_ipm:
	@echo "Building the project for iPhone 15 Pro Max..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone 15 Pro Max" build

build_ise3:
	@echo "Building the project for iPhone SE (3rd generation)..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone SE (3rd generation)" build

# Clean the build folders for all simulators
clean:
	@echo "Cleaning the build folders for all simulators..."
	@$(CLEAN_SCRIPT)

# Clean the build folder for specific simulators using aliases
clean_ip:
	@echo "Cleaning the build folder for iPhone 15 Pro..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone 15 Pro" clean

clean_ipm:
	@echo "Cleaning the build folder for iPhone 15 Pro Max..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone 15 Pro Max" clean

clean_ise3:
	@echo "Cleaning the build folder for iPhone SE (3rd generation)..."
	@xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=iPhone SE (3rd generation)" clean

# Fetch dependencies
fetch:
	@echo "Fetching dependencies..."
	@$(FETCH_SCRIPT)

# Simulate the app on all simulators
simulate:
	@echo "Simulating the app..."
	@$(SIMULATE_SCRIPT)

# Help command
help:
	@echo "Makefile commands:"
	@echo "  make build          - Build the project for all simulators"
	@echo "  make build_ip       - Build the project for iPhone 15 Pro"
	@echo "  make build_ipm      - Build the project for iPhone 15 Pro Max"
	@echo "  make build_ise3     - Build the project for iPhone SE (3rd generation)"
	@echo "  make clean          - Clean the build folders for all simulators"
	@echo "  make clean_ip       - Clean the build folder for iPhone 15 Pro"
	@echo "  make clean_ipm      - Clean the build folder for iPhone 15 Pro Max"
	@echo "  make clean_ise3     - Clean the build folder for iPhone SE (3rd generation)"
	@echo "  make fetch          - Fetch dependencies"
	@echo "  make simulate       - Simulate the app on all simulators"
	@echo "  make help           - Show this help message"
	@echo ""
	@echo "Aliases:"
	@echo "  all                 - Build for all simulators"
	@echo "  ip                  - Alias for iPhone 15 Pro"
	@echo "  ipm                 - Alias for iPhone 15 Pro Max"
	@echo "  ise3                - Alias for iPhone SE (3rd generation)"

rmdd:
	@rm -rf ~/Library/Developer/Xcode/DerivedData/CrateNFC*
