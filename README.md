# CrateNFC

## Introduction
CrateNFC writes song urls to NFC Chips.

## Development Setup

### For Xcode Users
First, download `swiftformat` via `% brew install swiftformat`.
Second, download `swiftformat-for-xcode` via `% brew install --cask swiftformat-for-xcode`.
Now we need to set a hot key for executing `% swiftformat .` on-save.

![image](https://github.com/user-attachments/assets/bb59ff04-17ba-4430-acaa-7ea382a2bb25)

Go to `System Settings`, search for `Keyboard shortcuts`, click it, then navigate to `App Shortcuts` and replicate the screenshot above.

### Install Pre-commit

You can use either pip `% pip install pre-commit` or brew `% brew install pre-commit` to install pre-commit.

### Install the Git Hook Scripts Locally

To set up the git hook scripts locally, you need to do this command:
`% pre-commit install`

### Set Pre-Commit Alias

I recommend appending `pre='pre-commit run --all-files'` to `~/.zshrc`. Now you can do `% pre` to run your pre-commit checks before doing `% git add -p`.

###

## Running CrateNFC
You need to choose between development or production servers while running the simulator.

## Testing CrateNFC
![image](https://github.com/user-attachments/assets/7b18060b-055a-4198-9208-f248feffcc69)
Navigate here, hover your mouse to the right of the tests, and press the run button that appears.
You can either run every test function, a whole file's test functions, or an individual test function depending on what you select.

### Toggling Servers

<img src="https://github.com/user-attachments/assets/fbb11d78-ad16-41c9-a72f-3ebe977147c5" alt="Example Image" width="300" />

Navigate to `Profile` and select your server.

### Note on Development Server
Make sure you've cloned [https://github.com/lilrobo/CrateAPINET/](https://github.com/lilrobo/CrateNFCAPI) and you are running it on docker. If postgres is running for some inexplicable reason elsewhere, just do `% sudo pkill -u postgres`.
