// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		253C27AB2D2C61A800E0DFEB /* libCrateServices.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 253C27142D2C387A00E0DFEB /* libCrateServices.a */; };
		25C0D7C12D309FE400761251 /* Factory in Frameworks */ = {isa = PBXBuildFile; productRef = 25C0D7C02D309FE400761251 /* Factory */; };
		936F76882BEC3C5000868396 /* CrateShare.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 936F767E2BEC3C5000868396 /* CrateShare.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		93E405BE2CAFF62700C41B07 /* CrateAction.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 93E405B02CAFF62700C41B07 /* CrateAction.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		CFEAC08A2DD4632600C095A6 /* MSAL in Frameworks */ = {isa = PBXBuildFile; productRef = CFEAC0892DD4632600C095A6 /* MSAL */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		253C27132D2C387A00E0DFEB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 253C270D2D2C387900E0DFEB /* CrateServices.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 253C27032D2C387800E0DFEB;
			remoteInfo = CrateServices;
		};
		936F76862BEC3C5000868396 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 93FCAA352B967C4A00C9384F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 936F767D2BEC3C5000868396;
			remoteInfo = CrateShare;
		};
		9384BD9F2D91643D002B243A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 93FCAA352B967C4A00C9384F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 93FCAA3C2B967C4A00C9384F;
			remoteInfo = CrateNFC;
		};
		93E405BC2CAFF62700C41B07 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 93FCAA352B967C4A00C9384F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 93E405AF2CAFF62700C41B07;
			remoteInfo = CrateAction;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		936F76892BEC3C5000868396 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				93E405BE2CAFF62700C41B07 /* CrateAction.appex in Embed Foundation Extensions */,
				936F76882BEC3C5000868396 /* CrateShare.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		93D319CC2D31912C00C69B6D /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		253C270D2D2C387900E0DFEB /* CrateServices.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = CrateServices.xcodeproj; path = ../CrateServices/CrateServices.xcodeproj; sourceTree = SOURCE_ROOT; };
		934D69602D916F98002B024A /* CrateNFC.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = CrateNFC.xctestplan; sourceTree = "<group>"; };
		936F767E2BEC3C5000868396 /* CrateShare.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = CrateShare.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		9384BD9B2D91643D002B243A /* CrateNFCTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CrateNFCTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		93C06A342BE0555500DB1387 /* CoreNFC.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreNFC.framework; path = System/Library/Frameworks/CoreNFC.framework; sourceTree = SDKROOT; };
		93E405B02CAFF62700C41B07 /* CrateAction.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = CrateAction.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		93E405B12CAFF62700C41B07 /* UniformTypeIdentifiers.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UniformTypeIdentifiers.framework; path = System/Library/Frameworks/UniformTypeIdentifiers.framework; sourceTree = SDKROOT; };
		93F0B9482D18C0BC00F4D18E /* .swiftformat */ = {isa = PBXFileReference; lastKnownFileType = text; path = .swiftformat; sourceTree = "<group>"; };
		93FCAA3D2B967C4A00C9384F /* CrateNFC.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CrateNFC.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		93D3E3C62D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				CrateAction/ActionViewController.swift,
				CrateAction/Info.plist,
				CrateNFC/Info.plist,
				CrateShare/Info.plist,
				CrateShare/ShareViewController.swift,
			);
			target = 93FCAA3C2B967C4A00C9384F /* CrateNFC */;
		};
		93D3E3C72D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				CrateShare/ShareViewController.swift,
			);
			target = 936F767D2BEC3C5000868396 /* CrateShare */;
		};
		93D3E3C82D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				CrateAction/ActionViewController.swift,
			);
			target = 93E405AF2CAFF62700C41B07 /* CrateAction */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		9384BD9C2D91643D002B243A /* CrateNFCTests */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = CrateNFCTests; sourceTree = "<group>"; };
		93D3E3A32D2DCF8C00AE7B0C /* App */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (93D3E3C62D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */, 93D3E3C82D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */, 93D3E3C72D2DCF8C00AE7B0C /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = App; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		936F767B2BEC3C5000868396 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9384BD982D91643D002B243A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93E405AD2CAFF62700C41B07 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93FCAA3A2B967C4A00C9384F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				253C27AB2D2C61A800E0DFEB /* libCrateServices.a in Frameworks */,
				CFEAC08A2DD4632600C095A6 /* MSAL in Frameworks */,
				25C0D7C12D309FE400761251 /* Factory in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		253C27102D2C387900E0DFEB /* Products */ = {
			isa = PBXGroup;
			children = (
				253C27142D2C387A00E0DFEB /* libCrateServices.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		93C06A332BE0555500DB1387 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				93C06A342BE0555500DB1387 /* CoreNFC.framework */,
				93E405B12CAFF62700C41B07 /* UniformTypeIdentifiers.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		93FCAA342B967C4A00C9384F = {
			isa = PBXGroup;
			children = (
				93D3E3A32D2DCF8C00AE7B0C /* App */,
				253C270D2D2C387900E0DFEB /* CrateServices.xcodeproj */,
				9384BD9C2D91643D002B243A /* CrateNFCTests */,
				93F0B9482D18C0BC00F4D18E /* .swiftformat */,
				934D69602D916F98002B024A /* CrateNFC.xctestplan */,
				93C06A332BE0555500DB1387 /* Frameworks */,
				93FCAA3E2B967C4A00C9384F /* Products */,
			);
			sourceTree = "<group>";
		};
		93FCAA3E2B967C4A00C9384F /* Products */ = {
			isa = PBXGroup;
			children = (
				93FCAA3D2B967C4A00C9384F /* CrateNFC.app */,
				936F767E2BEC3C5000868396 /* CrateShare.appex */,
				93E405B02CAFF62700C41B07 /* CrateAction.appex */,
				9384BD9B2D91643D002B243A /* CrateNFCTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		936F767D2BEC3C5000868396 /* CrateShare */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 936F768C2BEC3C5000868396 /* Build configuration list for PBXNativeTarget "CrateShare" */;
			buildPhases = (
				936F767A2BEC3C5000868396 /* Sources */,
				936F767B2BEC3C5000868396 /* Frameworks */,
				936F767C2BEC3C5000868396 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CrateShare;
			productName = CrateShare;
			productReference = 936F767E2BEC3C5000868396 /* CrateShare.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		9384BD9A2D91643D002B243A /* CrateNFCTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9384BDA12D91643D002B243A /* Build configuration list for PBXNativeTarget "CrateNFCTests" */;
			buildPhases = (
				9384BD972D91643D002B243A /* Sources */,
				9384BD982D91643D002B243A /* Frameworks */,
				9384BD992D91643D002B243A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9384BDA02D91643D002B243A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9384BD9C2D91643D002B243A /* CrateNFCTests */,
			);
			name = CrateNFCTests;
			packageProductDependencies = (
			);
			productName = CrateNFCTests;
			productReference = 9384BD9B2D91643D002B243A /* CrateNFCTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		93E405AF2CAFF62700C41B07 /* CrateAction */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 93E405C32CAFF62700C41B07 /* Build configuration list for PBXNativeTarget "CrateAction" */;
			buildPhases = (
				93E405AC2CAFF62700C41B07 /* Sources */,
				93E405AD2CAFF62700C41B07 /* Frameworks */,
				93E405AE2CAFF62700C41B07 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CrateAction;
			productName = CrateAction;
			productReference = 93E405B02CAFF62700C41B07 /* CrateAction.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		93FCAA3C2B967C4A00C9384F /* CrateNFC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 93FCAA612B967C4B00C9384F /* Build configuration list for PBXNativeTarget "CrateNFC" */;
			buildPhases = (
				93FCAA392B967C4A00C9384F /* Sources */,
				93FCAA3A2B967C4A00C9384F /* Frameworks */,
				93FCAA3B2B967C4A00C9384F /* Resources */,
				936F76892BEC3C5000868396 /* Embed Foundation Extensions */,
				93D319CC2D31912C00C69B6D /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				936F76872BEC3C5000868396 /* PBXTargetDependency */,
				93E405BD2CAFF62700C41B07 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				93D3E3A32D2DCF8C00AE7B0C /* App */,
			);
			name = CrateNFC;
			packageProductDependencies = (
				25C0D7C02D309FE400761251 /* Factory */,
				CFEAC0892DD4632600C095A6 /* MSAL */,
			);
			productName = CrateNFC;
			productReference = 93FCAA3D2B967C4A00C9384F /* CrateNFC.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		93FCAA352B967C4A00C9384F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					936F767D2BEC3C5000868396 = {
						CreatedOnToolsVersion = 15.1;
					};
					9384BD9A2D91643D002B243A = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 93FCAA3C2B967C4A00C9384F;
					};
					93E405AF2CAFF62700C41B07 = {
						CreatedOnToolsVersion = 15.1;
					};
					93FCAA3C2B967C4A00C9384F = {
						CreatedOnToolsVersion = 15.1;
					};
				};
			};
			buildConfigurationList = 93FCAA382B967C4A00C9384F /* Build configuration list for PBXProject "CrateNFC" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 93FCAA342B967C4A00C9384F;
			packageReferences = (
				25C0D7BF2D309FE400761251 /* XCRemoteSwiftPackageReference "Factory" */,
				CFEAC0882DD4631900C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */,
			);
			productRefGroup = 93FCAA3E2B967C4A00C9384F /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 253C27102D2C387900E0DFEB /* Products */;
					ProjectRef = 253C270D2D2C387900E0DFEB /* CrateServices.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				93FCAA3C2B967C4A00C9384F /* CrateNFC */,
				93E405AF2CAFF62700C41B07 /* CrateAction */,
				936F767D2BEC3C5000868396 /* CrateShare */,
				9384BD9A2D91643D002B243A /* CrateNFCTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		253C27142D2C387A00E0DFEB /* libCrateServices.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCrateServices.a;
			remoteRef = 253C27132D2C387A00E0DFEB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		936F767C2BEC3C5000868396 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9384BD992D91643D002B243A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93E405AE2CAFF62700C41B07 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93FCAA3B2B967C4A00C9384F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		936F767A2BEC3C5000868396 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9384BD972D91643D002B243A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93E405AC2CAFF62700C41B07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93FCAA392B967C4A00C9384F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		936F76872BEC3C5000868396 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 936F767D2BEC3C5000868396 /* CrateShare */;
			targetProxy = 936F76862BEC3C5000868396 /* PBXContainerItemProxy */;
		};
		9384BDA02D91643D002B243A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 93FCAA3C2B967C4A00C9384F /* CrateNFC */;
			targetProxy = 9384BD9F2D91643D002B243A /* PBXContainerItemProxy */;
		};
		93E405BD2CAFF62700C41B07 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 93E405AF2CAFF62700C41B07 /* CrateAction */;
			targetProxy = 93E405BC2CAFF62700C41B07 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		932CD21F2C1B71B700BB2712 /* Development */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMPILER_INDEX_STORE_ENABLE = NO;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEVELOPMENT $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Development;
		};
		932CD2202C1B71B700BB2712 /* Development */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = App/CrateNFC/CrateNFC.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				ENABLE_PREVIEWS = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				INFOPLIST_KEY_NFCReaderUsageDescription = "Write to an NFC chip.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_TREAT_WARNINGS_AS_ERRORS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Development;
		};
		932CD2222C1B71B700BB2712 /* Development */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateShare/CrateShare.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CrateShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateShare";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Development;
		};
		932CD2242C1B71C500BB2712 /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMPILER_INDEX_STORE_ENABLE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "PRODUCTION $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Production;
		};
		932CD2252C1B71C500BB2712 /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = App/CrateNFC/CrateNFC.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				ENABLE_PREVIEWS = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				INFOPLIST_KEY_NFCReaderUsageDescription = "Write to an NFC chip.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_TREAT_WARNINGS_AS_ERRORS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Production;
		};
		932CD2272C1B71C500BB2712 /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateShare/CrateShare.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CrateShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateShare";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Production;
		};
		936F768A2BEC3C5000868396 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateShare/CrateShare.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CrateShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateShare";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		936F768B2BEC3C5000868396 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateShare/CrateShare.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CrateShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateShare";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		9384BDA22D91643D002B243A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lilrobo.CrateNFCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CrateNFC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CrateNFC";
			};
			name = Debug;
		};
		9384BDA32D91643D002B243A /* Development */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lilrobo.CrateNFCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CrateNFC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CrateNFC";
			};
			name = Development;
		};
		9384BDA42D91643D002B243A /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lilrobo.CrateNFCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CrateNFC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CrateNFC";
			};
			name = Production;
		};
		9384BDA52D91643D002B243A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lilrobo.CrateNFCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CrateNFC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CrateNFC";
			};
			name = Release;
		};
		93E405BF2CAFF62700C41B07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateAction/CrateAction.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Write to NFC with Crate";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateAction";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		93E405C02CAFF62700C41B07 /* Development */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateAction/CrateAction.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Write to NFC with Crate";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateAction";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Development;
		};
		93E405C12CAFF62700C41B07 /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateAction/CrateAction.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Write to NFC with Crate";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateAction";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Production;
		};
		93E405C22CAFF62700C41B07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = App/CrateAction/CrateAction.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Write to NFC with Crate";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app.CrateAction";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		93FCAA5F2B967C4B00C9384F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMPILER_INDEX_STORE_ENABLE = NO;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Debug;
		};
		93FCAA602B967C4B00C9384F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMPILER_INDEX_STORE_ENABLE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		93FCAA622B967C4B00C9384F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = App/CrateNFC/CrateNFC.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				ENABLE_PREVIEWS = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				INFOPLIST_KEY_NFCReaderUsageDescription = "Write to an NFC chip.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_TREAT_WARNINGS_AS_ERRORS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		93FCAA632B967C4B00C9384F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = App/CrateNFC/CrateNFC.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				ENABLE_PREVIEWS = YES;
				FUSE_BUILD_SCRIPT_PHASES = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = App/CrateNFC/Info.plist;
				INFOPLIST_KEY_NFCReaderUsageDescription = "Write to an NFC chip.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = "com.lilrobo.cratenfc-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_TREAT_WARNINGS_AS_ERRORS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		936F768C2BEC3C5000868396 /* Build configuration list for PBXNativeTarget "CrateShare" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				936F768A2BEC3C5000868396 /* Debug */,
				932CD2222C1B71B700BB2712 /* Development */,
				932CD2272C1B71C500BB2712 /* Production */,
				936F768B2BEC3C5000868396 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9384BDA12D91643D002B243A /* Build configuration list for PBXNativeTarget "CrateNFCTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9384BDA22D91643D002B243A /* Debug */,
				9384BDA32D91643D002B243A /* Development */,
				9384BDA42D91643D002B243A /* Production */,
				9384BDA52D91643D002B243A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		93E405C32CAFF62700C41B07 /* Build configuration list for PBXNativeTarget "CrateAction" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				93E405BF2CAFF62700C41B07 /* Debug */,
				93E405C02CAFF62700C41B07 /* Development */,
				93E405C12CAFF62700C41B07 /* Production */,
				93E405C22CAFF62700C41B07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		93FCAA382B967C4A00C9384F /* Build configuration list for PBXProject "CrateNFC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				93FCAA5F2B967C4B00C9384F /* Debug */,
				932CD21F2C1B71B700BB2712 /* Development */,
				932CD2242C1B71C500BB2712 /* Production */,
				93FCAA602B967C4B00C9384F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		93FCAA612B967C4B00C9384F /* Build configuration list for PBXNativeTarget "CrateNFC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				93FCAA622B967C4B00C9384F /* Debug */,
				932CD2202C1B71B700BB2712 /* Development */,
				932CD2252C1B71C500BB2712 /* Production */,
				93FCAA632B967C4B00C9384F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		25C0D7BF2D309FE400761251 /* XCRemoteSwiftPackageReference "Factory" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hmlongco/Factory";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.3;
			};
		};
		CFEAC0882DD4631900C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AzureAD/microsoft-authentication-library-for-objc";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		25C0D7C02D309FE400761251 /* Factory */ = {
			isa = XCSwiftPackageProductDependency;
			package = 25C0D7BF2D309FE400761251 /* XCRemoteSwiftPackageReference "Factory" */;
			productName = Factory;
		};
		CFEAC0892DD4632600C095A6 /* MSAL */ = {
			isa = XCSwiftPackageProductDependency;
			package = CFEAC0882DD4631900C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */;
			productName = MSAL;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 93FCAA352B967C4A00C9384F /* Project object */;
}
