import Foundation

/// Password validation utility for Microsoft Entra External ID requirements
public struct PasswordValidator {

  /// Validates password against Microsoft Entra External ID requirements
  /// - Parameter password: The password to validate
  /// - Returns: True if password meets all requirements, false otherwise
  public static func isValidEntraPassword(_ password: String) -> <PERSON><PERSON> {
    return validatePasswordRequirements(password).isValid
  }

  /// Detailed password validation with specific requirement results
  /// - Parameter password: The password to validate
  /// - Returns: PasswordValidationResult with detailed validation info
  public static func validatePasswordRequirements(_ password: String) -> PasswordValidationResult {
    let lengthValid = password.count >= 8 && password.count <= 256
    let hasUppercase = password.rangeOfCharacter(from: .uppercaseLetters) != nil
    let hasLowercase = password.rangeOfCharacter(from: .lowercaseLetters) != nil
    let hasNumbers = password.rangeOfCharacter(from: .decimalDigits) != nil
    let hasSymbols = password.rangeOfCharacter(from: .symbols.union(.punctuationCharacters)) != nil

    // Count how many character groups are present
    let characterGroupCount = [hasUppercase, hasLowercase, hasNumbers, hasSymbols].filter { $0 }
      .count
    let characterGroupsValid = characterGroupCount >= 3

    let isValid = lengthValid && characterGroupsValid

    return PasswordValidationResult(
      isValid: isValid,
      lengthValid: lengthValid,
      hasUppercase: hasUppercase,
      hasLowercase: hasLowercase,
      hasNumbers: hasNumbers,
      hasSymbols: hasSymbols,
      characterGroupsValid: characterGroupsValid,
      characterGroupCount: characterGroupCount
    )
  }

  /// Get user-friendly validation error messages
  /// - Parameter password: The password to validate
  /// - Returns: Array of error messages for failed requirements
  public static func getValidationErrors(_ password: String) -> [String] {
    let result = validatePasswordRequirements(password)
    var errors: [String] = []

    if !result.lengthValid {
      errors.append("Password must be between 8 and 256 characters")
    }

    if !result.characterGroupsValid {
      let missing = 3 - result.characterGroupCount
      errors.append(
        "Need \(missing) more character groups (uppercase, lowercase, numbers, symbols)")
    }

    return errors
  }

  /// Get password requirements as help text
  /// - Returns: Array of requirement descriptions
  public static func getPasswordRequirements() -> [String] {
    return [
      "Be between 8 and 256 characters",
      "Contain at least 3 of the following 4 character groups:",
      "• Uppercase letters (A–Z)",
      "• Lowercase letters (a–z)",
      "• Numbers (0–9)",
      "• Symbols (!@#$%^&*()-_=+[]{}|;:'\",./<>?/)"
    ]
  }
}

/// Detailed result of password validation
public struct PasswordValidationResult {
  public let isValid: Bool
  public let lengthValid: Bool
  public let hasUppercase: Bool
  public let hasLowercase: Bool
  public let hasNumbers: Bool
  public let hasSymbols: Bool
  public let characterGroupsValid: Bool
  public let characterGroupCount: Int

  /// Get character groups that are present
  public var presentGroups: [String] {
    var groups: [String] = []
    if hasUppercase { groups.append("Uppercase") }
    if hasLowercase { groups.append("Lowercase") }
    if hasNumbers { groups.append("Numbers") }
    if hasSymbols { groups.append("Symbols") }
    return groups
  }

  /// Get character groups that are missing
  public var missingGroups: [String] {
    var groups: [String] = []
    if !hasUppercase { groups.append("Uppercase letters") }
    if !hasLowercase { groups.append("Lowercase letters") }
    if !hasNumbers { groups.append("Numbers") }
    if !hasSymbols { groups.append("Symbols") }
    return groups
  }
}
