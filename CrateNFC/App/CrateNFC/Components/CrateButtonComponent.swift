import SwiftUI

struct CrateButtonComponent: View {
  let title: String?
  let image: String?
  let action: () -> Void
  let textColor: Color
  let outlineColor: Color
  let isPrimary: Bool

  init(
    title: String? = nil,
    image: String? = nil,
    action: @escaping () -> Void,
    textColor: Color? = nil,
    outlineColor: Color? = nil,
    isPrimary: Bool = false
  ) {
    self.title = title
    self.image = image
    self.action = action
    self.isPrimary = isPrimary
    self.textColor = textColor ?? (isPrimary ? Color("primaryButtonText") : .primary)
    self.outlineColor = outlineColor ?? (isPrimary ? Color("primaryButtonColor") : .primary)
  }

  var body: some View {
    Button(action: action) {
      HStack {
        if let image = image, !image.isEmpty {
          Image(systemName: image)
            .resizable()
            .scaledToFit()
            .frame(width: 20, height: 20)
            .foregroundColor(Color.primaryButtonText)
        }
        if let title = title, !title.isEmpty {
          Text(title)
            .foregroundColor(Color.primaryButtonText)
            .fontWeight(.bold)
            .lineLimit(1)
            .truncationMode(.tail)
            .frame(maxWidth: .infinity)
            .multilineTextAlignment(.center)
        }
      }
      .padding()
      .frame(minHeight: 44)
      .background(Color.primaryButton)
      .cornerRadius(10)
    }
  }
}
