import SwiftUI

struct CrateViewComponent<Content: View>: View {
  let content: Content
  let horizontalPadding: CGFloat

  init(horizontalPadding: CGFloat = 16, @ViewBuilder content: () -> Content) {
    self.content = content()
    self.horizontalPadding = horizontalPadding
  }

  var body: some View {
    content
      .padding(.horizontal, horizontalPadding)
      .frame(maxWidth: .infinity)
  }
}
