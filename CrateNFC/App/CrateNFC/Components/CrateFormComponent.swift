import SwiftUI

struct CrateFormComponent: View {
  @Binding var text: String
  let placeholder: String
  let onClearClicked: (() -> Void)?

  @FocusState private var isFocused: Bool

  var body: some View {
    VStack {
      ZStack(alignment: .topLeading) {
        TextField(placeholder, text: $text)
          .focused($isFocused)
          .lineLimit(1)
          // .truncationMode(.head)
          .padding(.leading, 10)
          .padding(.trailing, 40)
          .frame(height: 40)
          .autocapitalization(.none)
          .overlay(
            RoundedRectangle(cornerRadius: 5)
              .stroke(Color.primary, lineWidth: 2)
              .shadow(radius: 5)
          )

        if !text.isEmpty {
          clearButton
        }
      }
      .contentShape(Rectangle())
    }
    .frame(width: 305)
    .padding(.bottom, 10)
    .onTapGesture {
      isFocused = true
    }
  }

  private var clearButton: some View {
    Button(
      action: {
        text = ""
        onClearClicked?()
      },
      label: {
        Image(systemName: "xmark.circle.fill")
          .foregroundColor(.secondary )
          .padding(.trailing, 10)
          .padding(.top, 10)
      }
    ).frame(maxWidth: .infinity, alignment: .topTrailing)
  }
}
