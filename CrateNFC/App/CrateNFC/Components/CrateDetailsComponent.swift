import SwiftUI

struct CrateDetails: View {
  let details: [(title: String, content: String, isUrl: Bool)]

  init(details: [(title: String, content: String, isUrl: Bool)]) {
    self.details = details
  }

  // will refactor the isURL stuff later, will probably add an enum "detailsType"
  var body: some View {
    VStack(spacing: 12) {
      ForEach(details.indices, id: \.self) { index in
        if !details[index].content.isEmpty {
          detailRow(details[index].title, content: details[index].content, isUrl: details[index].isUrl)
        }
      }
    }
  }

  // We need to do this because iphones fail to open a url unless its prefixed with https by default... stupid I know.
  // We likely need to have this sort of handling in place everywhere for a link or just remember to do this.
  // We can solve this by writing the full URL to the nfc card every time (including https if it doesn't have it already)
  private func validateAndFormatURL(_ urlString: String) -> URL? {
    let cleanedString = urlString.trimmingCharacters(in: .controlCharacters)
    if var components = URLComponents(string: cleanedString) {
      if components.scheme == nil {
        components.scheme = "https"
      }
      return components.url
    }
    return nil
  }

  private func detailRow(_ title: String, content: String, isUrl: Bool) -> some View {
    VStack(alignment: .leading, spacing: 5) {
      Text(title)
        .font(.headline)
        .foregroundColor(Color.primary)

      if isUrl, let url = validateAndFormatURL(content) {
        Link(content, destination: url)
          .font(.body)
          .foregroundColor(.blue)
          .lineLimit(1)
          .truncationMode(.tail)
          .textSelection(.enabled)
      } else {
        Text(content)
          .font(.body)
          .foregroundColor(Color.primary)
          .lineLimit(1)
          .truncationMode(.tail)
          .textSelection(.enabled)
      }
    }
    .padding()
    .frame(maxWidth: .infinity, alignment: .leading)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .stroke(Color.primary, lineWidth: 2)
    )
    .cornerRadius(12)
  }
}
