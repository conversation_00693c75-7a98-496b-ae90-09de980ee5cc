// import SwiftData
// import SwiftUI
//
// struct URLItem: Identifiable {
//  var id: String { url.absoluteString }
//  var url: URL
// }
//
// struct CardDesignView: View {
//  let cards = [
//    (
//      "Gold Text Black Tape Cassette",
//      "blk-gld-2",
//      "https://lilrobo.xyz/products/gold-text-black-tape-cassette-triangle-cutout-nfc-card"
//    ),
//    ("Red Stripe Tape Cassette", "red-2", "https://lilrobo.xyz/products/red-stripe-tape-cassette-nfc-card"),
//    ("Bronze Wheel Clear Tape Cassette", "gold-2", "https://lilrobo.xyz/products/gold-wheel-clear-tape-cassette-nfc-card-copy"),
//    ("Hot Pink Label Black Tape Cassette", "Artboard3", "https://lilrobo.xyz/products/hot-pink-label-black-tape-cassette-nfc-card"),
//    ("Yellow Tape Cassette", "Yellow-2", "https://lilrobo.xyz/products/yellow-tape-cassette-nfc-card"),
//    ("Light Grey Tape Cassette", "gray-2", "https://lilrobo.xyz/products/nfc-mixtape-cassette-light-grey-distressed")
//  ]
//
//  @State private var selectedURLItem: URLItem?
//  @Binding private var selectedTab: NavView.Tab
//  private var deepLinkHandler: DeepLinkHandler
//
//  init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
//    _selectedTab = selectedTab
//    self.deepLinkHandler = deepLinkHandler
//
//    UIPageControl.appearance().currentPageIndicatorTintColor = UIColor(Color.primary)
//    UIPageControl.appearance().pageIndicatorTintColor = UIColor(Color.secondary)
//  }
//
//  var body: some View {
//    TabView {
//      ForEach(cards, id: \.0) { card in
//        FullScreenCardView(
//          title: card.0,
//          imageName: card.1,
//          urlString: card.2,
//          selectedURLItem: $selectedURLItem
//        )
//      }
//    }
//    .tabViewStyle(.page(indexDisplayMode: .always))
//    .sheet(item: $selectedURLItem) { urlItem in
//      WebView(url: urlItem.url)
//    }
//  }
// }
//
// struct FullScreenCardView: View {
//  var title: String
//  var imageName: String
//  var urlString: String
//  @Binding var selectedURLItem: URLItem?
//
//  var body: some View {
//    VStack {
//      Spacer()
//
//      Image(imageName)
//        .resizable()
//        .scaledToFit()
//        .frame(maxWidth: .infinity)
//        .padding(.horizontal, 20)
//
//      Text(title)
//        .font(.title2)
//        .bold()
//        .foregroundColor(.primary)
//        .padding(.top, 20)
//
//      Spacer()
//    }
//    .frame(maxWidth: .infinity, maxHeight: .infinity)
//    .background(Color(UIColor.systemBackground))
//    .onTapGesture {
//      if let url = URL(string: urlString) {
//        selectedURLItem = URLItem(url: url)
//      }
//    }
//  }
// }
//
// #Preview {
//  CardDesignView(
//    selectedTab: .constant(.cardDesign),
//    deepLinkHandler: DeepLinkHandler()
//  )
// }
