import CrateServices
import Factory
import SwiftData
import SwiftUI

class ProfileAuthViewModel: ObservableObject {
  @Published private(set) var currentUser: User?
  private let userService: UserServiceProtocol

  init(
    userService: UserServiceProtocol = Container.shared.userService.resolve(),
    userState: UserState = Container.shared.userState.resolve()
  ) {
    self.userService = userService
    currentUser = userState.currentUser
  }

  var email: String {
    currentUser?.email ?? ""
  }

  func signOut(completion: @escaping () -> Void) {
    userService.logout()
    completion()
  }
}
