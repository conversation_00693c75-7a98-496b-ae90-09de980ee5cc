import SwiftUI

struct ProfileAuthView: View {
  @StateObject private var viewModel = ProfileAuthViewModel()
  @Environment(\.presentationMode) private var presentationMode
  @State private var showingPasswordReset = false

  var body: some View {
    NavigationView {
      List {
        getAccountSection()
        getPasswordSection()
        getSignOutSection()
      }
      .navigationTitle("My Account")
      .navigationBarTitleDisplayMode(.inline)
      .navigationBarItems(trailing: getDoneButton())
    }
    .sheet(isPresented: $showingPasswordReset) {
      PasswordResetView()
    }
  }

  // MARK: - View Components

  @ViewBuilder
  private func getAccountSection() -> some View {
    Section(header: Text("Account")) {
      getEmailRow()
    }
  }

  @ViewBuilder
  private func getPasswordSection() -> some View {
    Section(header: Text("Password")) {
      getResetPasswordButton()
    }
  }

  @ViewBuilder
  private func getSignOutSection() -> some View {
    Section {
      getSignOutButton()
    }
  }

  // MARK: - Helper Views

  @ViewBuilder
  private func getEmailRow() -> some View {
    HStack {
      Text("Email")
      Spacer()
      Text(viewModel.email)
        .foregroundColor(.secondary)
    }
  }

  @ViewBuilder
  private func getResetPasswordButton() -> some View {
    Button(
      action: {
        showingPasswordReset = true
      },
      label: {
        HStack {
          Text("Reset Password")
          Spacer()
          Image(systemName: "chevron.right")
            .foregroundColor(.secondary)
            .font(.caption)
        }
      })
  }

  @ViewBuilder
  private func getSignOutButton() -> some View {
    Button(
      action: {
        viewModel.signOut {
          presentationMode.wrappedValue.dismiss()
        }
      },
      label: {
        HStack {
          Spacer()
          Text("Sign Out")
            .foregroundColor(.red)
          Spacer()
        }
      })
  }

  @ViewBuilder
  private func getDoneButton() -> some View {
    Button("Done") {
      presentationMode.wrappedValue.dismiss()
    }
  }
}

#Preview {
  ProfileAuthView()
}
