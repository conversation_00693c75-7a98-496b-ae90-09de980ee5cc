import SwiftUI

@MainActor
final public class AppSettingsViewModel: ObservableObject {
  public enum PreferredOS: String, CaseIterable, Identifiable {
    case iOS
    case android = "Android"
    public var id: String { rawValue }
  }

  @Published public var serverURL: String =
    UserDefaults.standard.serverURL ?? "https://api.cratenfc.com"
  @Published public var selectedOS =
    PreferredOS(rawValue: UserDefaults.standard.preferredOS ?? "iOS") ?? .iOS
  @Published public var streamingAppDefault: String =
    UserDefaults.standard.streamingAppDefault ?? "Spotify"

}
