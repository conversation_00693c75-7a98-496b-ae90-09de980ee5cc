import Foundation
import SwiftUI

struct AppSettingsView: View {
  private let appVersion: String =
    Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"
  private let buildNumber: String =
    Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "1"
  let standardURLs = [
    URL(string: "https://api.cratenfc.com")!,
    URL(string: "http://192.168.1.64:8000")!
  ]
  @StateObject private var viewModel: AppSettingsViewModel
  public init() {
    _viewModel = StateObject(wrappedValue: AppSettingsViewModel())
  }

  var body: some View {
    NavigationView {
      Form {
        getServerSettingsSection()
        getPreferencesSection()
        getAppInformationSection()
      }
      .navigationTitle("Settings")
      .padding()
    }
  }

  private var copyrightText: String {
    let currentYear = Calendar.current.component(.year, from: Date())
    return "©️ \(currentYear) Lilrobo Inc. All rights reserved."
  }

  // MARK: - View Components

  @ViewBuilder
  private func getServerSettingsSection() -> some View {
    Section(header: Text("Server Settings")) {
      getServerURLPicker()
      getServerURLTextField()
      getCustomServerWarning()
    }
  }

  private func getPreferencesSection() -> some View {
    Section(header: Text("Preferences")) {
      getOSPicker()
      getStreamingAppPicker()
    }
  }

  private func getAppInformationSection() -> some View {
    Section {
      VStack(alignment: .center, spacing: 8) {
        getVersionInfo()
        getCopyrightInfo()
        getWebsiteLink()
      }
      .frame(maxWidth: .infinity)
    }
  }

  // MARK: - Helper Views

  @ViewBuilder
  private func getServerURLPicker() -> some View {
    Picker("Server URL", selection: $viewModel.serverURL) {
      ForEach(standardURLs, id: \.absoluteString) { serverURL in
        Text(serverURL.absoluteString).tag(serverURL.absoluteString)
      }
    }
    .pickerStyle(SegmentedPickerStyle())
    .onChange(of: viewModel.serverURL) { _, newValue in
      UserDefaults.standard.serverURL = newValue
      print("📡 Server URL changed to: \(newValue)")
    }
  }

  @ViewBuilder
  private func getServerURLTextField() -> some View {
    TextField("Server URL", text: $viewModel.serverURL)
      .textInputAutocapitalization(.never)
      .autocorrectionDisabled()
  }

  @ViewBuilder
  private func getCustomServerWarning() -> some View {
    let standardURLStrings = standardURLs.map { $0.absoluteString }
    if !standardURLStrings.contains(viewModel.serverURL) {
      Text("⚠️ Using custom server")
        .font(.footnote)
        .foregroundColor(.orange)
    }
  }

  @ViewBuilder
  private func getOSPicker() -> some View {
    Picker("Preferred OS", selection: $viewModel.selectedOS) {
      ForEach(AppSettingsViewModel.PreferredOS.allCases) { operatingSystem in
        Text(operatingSystem.rawValue).tag(operatingSystem)
      }
    }
    .pickerStyle(SegmentedPickerStyle())
    .onChange(of: viewModel.selectedOS) { _, newValue in
      UserDefaults.standard.preferredOS = newValue.rawValue
    }
  }

  @ViewBuilder
  private func getStreamingAppPicker() -> some View {
    Picker("Streaming App Default", selection: $viewModel.streamingAppDefault) {
      ForEach(
        ["Apple Music", "Pandora", "SoundCloud", "Sound.xyz", "Spotify", "Spinamp", "YouTube"],
        id: \.self
      ) { service in
        Text(service).tag(service)
      }
    }
    .onChange(of: viewModel.streamingAppDefault) { _, newValue in
      UserDefaults.standard.streamingAppDefault = newValue
    }
  }

  @ViewBuilder
  private func getVersionInfo() -> some View {
    Text("App Version: \(appVersion) (\(buildNumber))")
      .font(.footnote)
      .multilineTextAlignment(.center)
  }

  @ViewBuilder
  private func getCopyrightInfo() -> some View {
    Text(copyrightText)
      .font(.footnote)
      .multilineTextAlignment(.center)
  }

  @ViewBuilder
  private func getWebsiteLink() -> some View {
    if let websiteURL = URL(string: "https://lilrobo.xyz") {
      Link("Visit our website", destination: websiteURL)
        .font(.footnote)
        .foregroundColor(.blue)
    }
  }
}

#Preview {
  AppSettingsView()
}
