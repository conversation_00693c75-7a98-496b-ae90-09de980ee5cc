import CrateServices
import Factory
import SwiftData
import SwiftUI

struct AddUrlToCollectionSheet: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewModel: AddUrlToCollectionViewModel
  @FocusState private var isFocused: Bool
  @Environment(\.modelContext) private var modelContext
  let onContentAdded: (() -> Void)?

  init(modelContext: ModelContext, collection: Collection, onContentAdded: (() -> Void)? = nil) {
    self.onContentAdded = onContentAdded
    _viewModel = StateObject(
      wrappedValue: AddUrlToCollectionViewModel(
        modelContext: modelContext,
        collection: collection,
        onContentAdded: onContentAdded
      ))
  }

  private func applyEventHandlers(_ content: some View) -> some View {
    content
      .onAppear()
      .onTapGesture {
        isFocused = false
      }
      .onChange(of: viewModel.viewState.enteredURL) { _, newValue in
        viewModel.viewState.currentURL = newValue
        viewModel.handleURLChange()
      }
      .alert(
        "Empty URL",
        isPresented: Binding(
          get: { viewModel.activeFlags.contains(.showEmptyUrlAlert) },
          set: { if !$0 { viewModel.activeFlags.remove(.showEmptyUrlAlert) } }
        )
      ) {
        Button("OK", role: .cancel) {}
      } message: {
        Text("Please enter a valid URL before adding to collection.")
      }
  }

  var body: some View {
    NavigationView {
      CrateViewComponent {
        applyEventHandlers(
          ScrollView {
            getMainContent()
          }
          .scrollIndicators(.hidden)
        )
      }
      .navigationTitle("Add to Collection")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .topBarLeading) {
          Button("Cancel") {
            dismiss()
          }
        }
      }
    }
  }

  @ViewBuilder
  private func getMainContent() -> some View {
    ScrollView {
      CrateHeadingComponent(title: "Preview")
      getPreviewSection()
      getUrlInputSection()
      getButtonSection()
    }
    .background(
      Color.clear
        .contentShape(Rectangle())
        .onTapGesture {
          isFocused = false
        }
    )
  }

  private func getPreviewSection() -> some View {
    VStack(alignment: .leading) {
      switch viewModel.state {
      case .empty:
        CardComponent(title: "", content: "")
      case .fetching:
        VStack {
          Spacer()
          ProgressView()
          Spacer()
        }
        .frame(height: UIScreen.main.bounds.height * 0.35 + 60)
      case let .loaded(record):
        CardComponent(
          title: record.detail ?? "",
          content: record.title ?? "",
          imageURL: viewModel.viewState.imageURL
        )
      case .error:
        VStack {
          CardComponent(title: "", content: "")
        }
        .frame(height: UIScreen.main.bounds.height * 0.35 + 60)
      }
    }
  }

  private func getUrlInputSection() -> some View {
    VStack(alignment: .center) {
      getPasteButton()
      CrateFormComponent(
        text: $viewModel.viewState.enteredURL,
        placeholder: "Enter URL here...",
        onClearClicked: viewModel.clearContentInfo
      )
      .focused($isFocused)
    }
  }

  private func getPasteButton() -> some View {
    Button(
      action: {
        if let clipboardString = UIPasteboard.general.string {
          viewModel.clearContentInfo()
          viewModel.viewState.enteredURL = clipboardString
        }
      },
      label: {
        HStack {
          Image(systemName: "doc.on.clipboard").imageScale(.small)
          Text("Paste from Clipboard").font(.subheadline)
        }
        .foregroundColor(.primary)
        .padding(.vertical, 8)
      })
  }

  private func getButtonSection() -> some View {
    HStack(spacing: 8) {
      CrateButtonComponent(
        title: "Add to Collection",
        image: "plus.circle",
        action: {
          switch viewModel.state {
          case .loaded, .error, .fetching:
            viewModel.handleAddToCollection()
            dismiss()
          case .empty:
            viewModel.activeFlags.insert(.showEmptyUrlAlert)
          }
          isFocused = false
        },
        textColor: Color.primary,
        outlineColor: Color.primary
      )
    }
    .frame(width: 300, height: 50)
  }
}
