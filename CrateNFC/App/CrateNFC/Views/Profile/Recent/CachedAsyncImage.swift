import SwiftUI

public struct CachedAsyncImage: View {
  let url: URL
  @State private var image: UIImage?
  @State private var isLoading = false

  public init(url: URL) {
    self.url = url
  }

  public var body: some View {
    Group {
      if let uiImage = image {
        Image(uiImage: uiImage)
          .resizable()
          .scaledToFit()
      } else if isLoading {
        ProgressView()
      } else {
        Image(systemName: "photo.fill")
          .resizable()
          .scaledToFit()
      }
    }
    .frame(width: 160, height: 160)
    .onAppear {
      guard image == nil, !isLoading else { return }
      isLoading = true
      URLSession.shared.dataTask(with: url) { data, _, _ in
        if let data = data, let uiImage = UIImage(data: data) {
          DispatchQueue.main.async {
            self.image = uiImage
          }
        }
        DispatchQueue.main.async {
          self.isLoading = false
        }
      }.resume()
    }
  }
}
