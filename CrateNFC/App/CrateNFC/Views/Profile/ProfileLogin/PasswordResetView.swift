import SwiftUI

struct PasswordResetView: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewModel = PasswordResetViewModel()
  @State private var isPasswordVisible = false

  var body: some View {
    NavigationView {
      ZStack {
        VStack(spacing: 20) {
          Text("Reset Password")
            .font(.largeTitle)
            .fontWeight(.bold)
            .foregroundColor(.primary)

          if viewModel.showNewPasswordFields {
            newPasswordView
          } else if viewModel.showVerificationCode {
            verificationCodeView
          } else {
            resetRequestView
          }

          Spacer()
        }
        .padding()

        if viewModel.isLoading {
          Color.black.opacity(0.4)
            .ignoresSafeArea()

          ProgressView("Loading...")
            .progressViewStyle(CircularProgressViewStyle(tint: .white))
            .foregroundColor(.white)
            .scaleEffect(1.5)
        }
      }
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Cancel") {
            dismiss()
          }
        }
      }
      .onChange(of: viewModel.isCompleted) { _, isCompleted in
        if isCompleted {
          // Auto-dismiss after 3 seconds to give user time to read success message
          DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            dismiss()
          }
        }
      }
    }
  }

  private var resetRequestView: some View {
    VStack(spacing: 20) {
      Text("Enter your email address to receive a password reset code")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      getField("Email", $viewModel.email)

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(viewModel.resetMessage.contains("sent") ? .green : .red)
      }

      CrateButtonComponent(
        title: "Send Reset Code",
        action: {
          Task {
            await viewModel.requestPasswordReset()
          }
        }
      )
      .disabled(viewModel.isLoading)
    }
  }

  private var verificationCodeView: some View {
    VStack(spacing: 20) {
      Text("Enter the verification code sent to your email")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      TextField("Verification Code", text: $viewModel.verificationCode)
        .keyboardType(.numberPad)
        .textContentType(.oneTimeCode)
        .padding()
        .cornerRadius(10)
        .overlay(
          RoundedRectangle(cornerRadius: 10)
            .stroke(Color.primary, lineWidth: 1)
        )
        .foregroundColor(.primary)

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(
            viewModel.resetMessage.contains("successfully")
              || viewModel.resetMessage.contains("successful") ? .green : .red)
      }

      VStack(spacing: 10) {
        CrateButtonComponent(
          title: "Submit",
          action: {
            Task {
              await viewModel.submitVerificationCode()
            }
          }
        )
        .disabled(viewModel.isLoading)

        Button("Resend Code") {
          Task {
            await viewModel.resendVerificationCode()
          }
        }
        .disabled(viewModel.isLoading)
      }
    }
  }

  private var newPasswordView: some View {
    VStack(spacing: 20) {
      if !viewModel.isCompleted {
        Text("Enter your new password")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)

        getPasswordField("New Password", $viewModel.newPassword)
        getField(
          "Confirm Password", $viewModel.confirmPassword, isSecure: true,
          isVisible: $isPasswordVisible)
      }

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(
            viewModel.resetMessage.contains("successfully")
              || viewModel.resetMessage.contains("successful") ? .green : .red)
      }

      if viewModel.isCompleted {
        CrateButtonComponent(
          title: "Done",
          action: {
            dismiss()
          }
        )
      } else {
        CrateButtonComponent(
          title: "Set New Password",
          action: {
            Task {
              await viewModel.submitNewPassword()
            }
          }
        )
        .disabled(viewModel.isLoading)

        // Password validation indicators
        if !viewModel.newPassword.isEmpty {
          getPasswordValidationIndicators(for: viewModel.newPassword)
        }
      }
    }
  }

  @ViewBuilder
  private func getField(
    _ fieldName: String, _ text: Binding<String>, isSecure: Bool = false,
    isVisible: Binding<Bool>? = nil
  )
  -> some View {
    HStack {
      let baseField = Group {
        if isSecure && !(isVisible?.wrappedValue ?? false) {
          SecureField(fieldName, text: text)
            .textContentType(
              fieldName.lowercased().contains("email")
                ? .emailAddress
                : fieldName.lowercased().contains("confirm") ? .password : .newPassword)
        } else {
          TextField(fieldName, text: text)
            .autocapitalization(.none)
            .textContentType(
              fieldName.lowercased().contains("email")
                ? .emailAddress
                : fieldName.lowercased().contains("confirm") ? .password : .newPassword)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()

      if isSecure, let visibilityBinding = isVisible {
        Button(
          action: {
            visibilityBinding.wrappedValue.toggle()
          },
          label: {
            Image(systemName: visibilityBinding.wrappedValue ? "eye.slash" : "eye")
              .foregroundColor(.secondary)
          }
        )
        .buttonStyle(PlainButtonStyle())
      }
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(Color.primary, lineWidth: 1)
    )
  }

  @ViewBuilder
  private func getPasswordField(_ fieldName: String, _ text: Binding<String>) -> some View {
    HStack {
      let baseField = Group {
        if !(isPasswordVisible) {
          SecureField(fieldName, text: text)
            .textContentType(.newPassword)
        } else {
          TextField(fieldName, text: text)
            .autocapitalization(.none)
            .textContentType(.newPassword)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()

      Button {
        isPasswordVisible.toggle()
      } label: {
        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
          .foregroundColor(.secondary)
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(getPasswordBorderColor(for: text.wrappedValue), lineWidth: 1)
    )
  }

  private func getPasswordBorderColor(for password: String) -> Color {
    if password.isEmpty {
      return Color.primary
    }

    let isValid = PasswordValidator.isValidEntraPassword(password)
    return isValid ? .green : .red
  }

  @ViewBuilder
  private func getPasswordValidationIndicators(for password: String) -> some View {
    let result = PasswordValidator.validatePasswordRequirements(password)

    VStack(alignment: .leading, spacing: 4) {
      getLengthValidationIndicator(result: result)
      getCharacterGroupsValidationIndicator(result: result)
      getIndividualCharacterGroupsIndicators(result: result)
    }
    .padding(.top, 4)
  }

  @ViewBuilder
  private func getLengthValidationIndicator(result: PasswordValidationResult) -> some View {
    HStack(spacing: 8) {
      Image(systemName: result.lengthValid ? "checkmark.circle.fill" : "xmark.circle.fill")
        .foregroundColor(result.lengthValid ? .green : .red)
        .font(.caption)
      Text("8-256 characters")
        .font(.caption)
        .foregroundColor(result.lengthValid ? .green : .secondary)
    }
  }

  @ViewBuilder
  private func getCharacterGroupsValidationIndicator(result: PasswordValidationResult) -> some View {
    HStack(spacing: 8) {
      Image(
        systemName: result.characterGroupsValid ? "checkmark.circle.fill" : "xmark.circle.fill"
      )
      .foregroundColor(result.characterGroupsValid ? .green : .red)
      .font(.caption)
      Text("At least 3 character groups:")
        .font(.caption)
        .foregroundColor(result.characterGroupsValid ? .green : .secondary)
    }
  }

  @ViewBuilder
  private func getIndividualCharacterGroupsIndicators(result: PasswordValidationResult) -> some View {
    VStack(alignment: .leading, spacing: 2) {
      getCharacterGroupIndicator(
        isValid: result.hasUppercase,
        text: "Uppercase letters (A-Z)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasLowercase,
        text: "Lowercase letters (a-z)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasNumbers,
        text: "Numbers (0-9)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasSymbols,
        text: "Symbols (!@#$%^&*)"
      )
    }
    .padding(.leading, 16)
  }

  @ViewBuilder
  private func getCharacterGroupIndicator(isValid: Bool, text: String) -> some View {
    HStack(spacing: 8) {
      Image(systemName: isValid ? "checkmark.circle.fill" : "circle")
        .foregroundColor(isValid ? .green : .secondary)
        .font(.caption2)
      Text(text)
        .font(.caption2)
        .foregroundColor(isValid ? .green : .secondary)
    }
  }
}
