import SwiftUI

struct ProfileRegisterView: View {
  @Environment(\.colorScheme) var colorScheme
  @StateObject private var viewModel: ProfileRegisterViewModel
  var onRegistrationComplete: () -> Void
  @State private var isPasswordVisible = false

  public init(onRegistrationComplete: @escaping () -> Void = {}) {
    _viewModel = StateObject(wrappedValue: ProfileRegisterViewModel())
    self.onRegistrationComplete = onRegistrationComplete
  }

  var body: some View {
    ZStack {
      VStack(spacing: 20) {
        Text("Register")
          .font(.largeTitle)
          .fontWeight(.bold)
          .foregroundColor(.primary)

        getField("Email", $viewModel.email)

        // Enhanced password field with validation
        getPasswordField()

        getField(
          "Confirm Password", $viewModel.confirmPassword, isSecure: true,
          isVisible: $isPasswordVisible)

        getButton(
          action: {
            Task {
              await viewModel.handleRegister()
            }
          }, text: "Register"
        )
        .disabled(
          viewModel.isLoading || !viewModel.isPasswordValid
            || viewModel.password != viewModel.confirmPassword)

        // Password validation indicators
        if !viewModel.password.isEmpty {
          getPasswordValidationIndicators()
        }

        if !viewModel.registrationMessage.isEmpty {
          Text(viewModel.registrationMessage)
            .font(.subheadline)
            .foregroundColor(viewModel.registrationMessage.contains("successful") ? .green : .red)
            .padding(.top, 10)
        }
      }

      if viewModel.isLoading {
        Color.black.opacity(0.4)
          .ignoresSafeArea()

        ProgressView("Loading...")
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .foregroundColor(.white)
          .scaleEffect(1.5)
      }
    }
    .sheet(isPresented: $viewModel.showVerificationCode) {
      VerificationCodeView(viewModel: viewModel)
    }
    .onChange(of: viewModel.registrationSuccessful) { _, success in
      if success {
        onRegistrationComplete()
      }
    }
  }

  @ViewBuilder
  private func getField(
    _ fieldName: String, _ text: Binding<String>, isSecure: Bool = false,
    isVisible: Binding<Bool>? = nil
  )
  -> some View {
    HStack {
      let baseField = Group {
        if isSecure && !(isVisible?.wrappedValue ?? false) {
          SecureField(fieldName, text: text)
            .textContentType(
              fieldName.lowercased().contains("email")
                ? .emailAddress
                : fieldName.lowercased().contains("confirm") ? .password : .newPassword)
        } else {
          TextField(fieldName, text: text)
            .autocapitalization(.none)
            .textContentType(
              fieldName.lowercased().contains("email")
                ? .emailAddress
                : fieldName.lowercased().contains("confirm") ? .password : .newPassword)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()

      if isSecure, let visibilityBinding = isVisible {
        Button(
          action: {
            visibilityBinding.wrappedValue.toggle()
          },
          label: {
            Image(systemName: visibilityBinding.wrappedValue ? "eye.slash" : "eye")
              .foregroundColor(.secondary)
          }
        )
        .buttonStyle(PlainButtonStyle())
      }
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(Color.primary, lineWidth: 1)
    )
  }

  @ViewBuilder
  private func getPasswordField() -> some View {
    getPasswordInputField()
  }

  @ViewBuilder
  private func getPasswordInputField() -> some View {
    HStack {
      let baseField = Group {
        if !isPasswordVisible {
          SecureField("Password", text: $viewModel.password)
            .textContentType(.newPassword)
        } else {
          TextField("Password", text: $viewModel.password)
            .autocapitalization(.none)
            .textContentType(.newPassword)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()
        .onChange(of: viewModel.password) { _, _ in
          viewModel.validatePassword()
        }

      Button {
        isPasswordVisible.toggle()
      } label: {
        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
          .foregroundColor(.secondary)
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(getPasswordBorderColor(), lineWidth: 1)
    )
  }

  @ViewBuilder
  private func getPasswordValidationIndicators() -> some View {
    let result = PasswordValidator.validatePasswordRequirements(viewModel.password)

    VStack(alignment: .leading, spacing: 4) {
      getLengthValidationIndicator(result: result)
      getCharacterGroupsValidationIndicator(result: result)
      getIndividualCharacterGroupsIndicators(result: result)
    }
    .padding(.top, 4)
  }

  @ViewBuilder
  private func getLengthValidationIndicator(result: PasswordValidationResult) -> some View {
    HStack(spacing: 8) {
      Image(systemName: result.lengthValid ? "checkmark.circle.fill" : "xmark.circle.fill")
        .foregroundColor(result.lengthValid ? .green : .red)
        .font(.caption)
      Text("8-256 characters")
        .font(.caption)
        .foregroundColor(result.lengthValid ? .green : .secondary)
    }
  }

  @ViewBuilder
  private func getCharacterGroupsValidationIndicator(result: PasswordValidationResult) -> some View {
    HStack(spacing: 8) {
      Image(
        systemName: result.characterGroupsValid ? "checkmark.circle.fill" : "xmark.circle.fill"
      )
      .foregroundColor(result.characterGroupsValid ? .green : .red)
      .font(.caption)
      Text("At least 3 character groups:")
        .font(.caption)
        .foregroundColor(result.characterGroupsValid ? .green : .secondary)
    }
  }

  @ViewBuilder
  private func getIndividualCharacterGroupsIndicators(result: PasswordValidationResult) -> some View {
    VStack(alignment: .leading, spacing: 2) {
      getCharacterGroupIndicator(
        isValid: result.hasUppercase,
        text: "Uppercase letters (A-Z)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasLowercase,
        text: "Lowercase letters (a-z)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasNumbers,
        text: "Numbers (0-9)"
      )

      getCharacterGroupIndicator(
        isValid: result.hasSymbols,
        text: "Symbols (!@#$%^&*)"
      )
    }
    .padding(.leading, 16)
  }

  @ViewBuilder
  private func getCharacterGroupIndicator(isValid: Bool, text: String) -> some View {
    HStack(spacing: 8) {
      Image(systemName: isValid ? "checkmark.circle.fill" : "circle")
        .foregroundColor(isValid ? .green : .secondary)
        .font(.caption2)
      Text(text)
        .font(.caption2)
        .foregroundColor(isValid ? .green : .secondary)
    }
  }

  private func getPasswordBorderColor() -> Color {
    if viewModel.password.isEmpty {
      return Color.primary
    }

    let isValid = PasswordValidator.isValidEntraPassword(viewModel.password)
    return isValid ? .green : .red
  }

  @ViewBuilder
  private func getButton(action: @escaping () -> Void, text: String) -> some View {
    CrateButtonComponent(
      title: text,
      action: action
    )
  }
}

#Preview {
  ProfileRegisterView()
}
