import SwiftUI

struct VerificationCodeView: View {
  @ObservedObject var viewModel: ProfileRegisterViewModel
  @Environment(\.dismiss) private var dismiss

  var body: some View {
    NavigationView {
      VStack(spacing: 20) {
        Text("Verify Email")
          .font(.largeTitle)
          .fontWeight(.bold)
          .foregroundColor(.primary)

        Text("Enter the verification code sent to your email")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)

        TextField("Verification Code", text: $viewModel.verificationCode)
          .keyboardType(.numberPad)
          .textContentType(.oneTimeCode)
          .padding()
          .cornerRadius(10)
          .overlay(
            RoundedRectangle(cornerRadius: 10)
              .stroke(Color.primary, lineWidth: 1)
          )
          .foregroundColor(.primary)

        if !viewModel.registrationMessage.isEmpty {
          Text(viewModel.registrationMessage)
            .font(.subheadline)
            .foregroundColor(viewModel.registrationMessage.contains("successful") ? .green : .red)
        }

        VStack(spacing: 10) {
          CrateButtonComponent(
            title: "Submit",
            action: {
              Task {
                await viewModel.submitVerificationCode()
              }
            }
          )
          .disabled(viewModel.isLoading)

          Button("Resend Code") {
            Task {
              await viewModel.resendVerificationCode()
            }
          }
          .disabled(viewModel.isLoading)
        }

        Spacer()
      }
      .padding()
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Cancel") {
            dismiss()
          }
        }
      }
    }
  }
}

#Preview {
  VerificationCodeView(viewModel: ProfileRegisterViewModel())
}
