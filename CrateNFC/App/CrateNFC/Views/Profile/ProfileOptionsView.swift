import CrateServices
import Factory
import SwiftUI

struct ProfileViewOptions: View {
  let showingSettings: Binding<Bool>
  let showingLogin: Binding<Bool>
  let isAuthenticated: Bool
  @StateObject private var userState = Container.shared.userState.resolve()

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Text("CrateNFC Profile")
          .font(.title2)
          .fontWeight(.semibold)
          .foregroundColor(.primary)

        Spacer()

        if userState.isSignedIn {
          NavigationLink(destination: ProfileAuthView()) {
            Image(systemName: "person.crop.circle")
              .resizable()
              .frame(width: 24, height: 24)
              .foregroundColor(.secondary)
          }
        } else {
          Button(
            action: { showingLogin.wrappedValue.toggle() },
            label: {
              Image(systemName: "person.crop.circle.dashed")
                .resizable()
                .frame(width: 24, height: 24)
                .foregroundColor(.secondary)
            })
        }

        getSettingsButton({ showingSettings.wrappedValue.toggle() }, "gearshape")
      }

    }
  }

  @ViewBuilder
  private func getSettingsButton(_ action: @escaping () -> Void, _ imageName: String) -> some View {
    Button(
      action: action,
      label: {
        Image(systemName: imageName)
          .resizable()
          .frame(width: 24, height: 24)
          .foregroundColor(.secondary)
      })
  }
}

#Preview {
  ProfileViewOptions(
    showingSettings: .constant(false),
    showingLogin: .constant(false),
    isAuthenticated: false
  )
}
