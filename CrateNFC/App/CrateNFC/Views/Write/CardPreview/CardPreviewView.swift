//
// // This is commented out until we use it
// import SwiftUI
//
// struct CardPreviewView: View {
//  @Environment(\.presentationMode) var presentationMode
//  @StateObject private var viewModel: CardPreviewModel
//
//  init(viewModel: CardPreviewModel) {
//    _viewModel = StateObject(wrappedValue: viewModel)
//  }
//
//  var body: some View {
//    VStack {
//      switch viewModel.state {
//      case .loading:
//        getLoadingState()
//      case .error(let message):
//        getErrorState(message)
//      case .content(let imageURL):
//        getImageState(imageURL: imageURL)
//      case .empty:
//        getNoImageState()
//      }
//
//      Text(viewModel.song)
//        .font(.headline)
//        .foregroundColor(.primary)
//        .padding(.bottom, 2)
//
//      Text(viewModel.artist)
//        .font(.subheadline)
//        .foregroundColor(.primary)
//
//      Spacer()
//
//      getButton({}, text: "Collect / Order")
//      getButton({presentationMode.wrappedValue.dismiss()}, text: "Dismiss")
//    }
//    .padding()
//    .background(Color.primary)
//    .cornerRadius(10)
//    .shadow(radius: 10)
//    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
//    .background(Color.primary.edgesIgnoringSafeArea(.all))
//    .onAppear {
//      viewModel.fetchCardPreview()
//    }
//  }
//  @ViewBuilder
//  func getButton(_ action: @escaping () -> Void, text: String) -> some View {
//    Button(action: action, label: {
//      Text(text)
//        .foregroundColor(.primary)
//        .padding()
//        .frame(maxWidth: .infinity)
//        .background(
//          RoundedRectangle(cornerRadius: 10)
//            .stroke(Color.primary, lineWidth: 2)
//        )
//    })
//    .frame(height: 44)
//    .padding(.bottom, 10)
//  }
//
//  @ViewBuilder
//  private func getLoadingState() -> some View {
//    ProgressView()
//      .frame(width: 300, height: 300)
//      .padding(.bottom, 10)
//  }
//  @ViewBuilder
//  private func getErrorState(_ errorMessage: String) -> some View {
//    Text(errorMessage)
//      .foregroundColor(.red)
//      .frame(width: 300, height: 300)
//      .padding(.bottom, 10)
//  }
//  @ViewBuilder
//  private func getNoImageState() -> some View {
//    Text("No image available")
//      .foregroundColor(.primary)
//      .frame(width: 300, height: 300)
//      .padding(.bottom, 10)
//  }
//  @ViewBuilder
//  private func getImageState(imageURL: URL) -> some View {
//    AsyncImage(url: imageURL) { phase in
//      switch phase {
//      case .empty:
//        ProgressView()
//      case let .success(image):
//        image
//          .resizable()
//          .aspectRatio(contentMode: .fill)
//      case .failure:
//        Image(systemName: "exclamationmark.triangle")
//      @unknown default:
//        EmptyView()
//      }
//    }
//    .frame(width: 300, height: 300)
//    .padding(.bottom, 10)
//  }
//
// }
//
// struct CardPreviewView_Previews: PreviewProvider {
//  static var previews: some View {
//    CardPreviewView(viewModel: CardPreviewModel(artist: "Wilco", song: "Reservations"))
//  }
// }
