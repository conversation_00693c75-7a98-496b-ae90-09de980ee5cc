//
// // Commented out until we use it
//
// import Foundation
//
// public enum CardPreviewState {
//  case loading
//  case error(String)
//  case empty
//  case content(URL)
// }
//
// @MainActor
// public final class CardPreviewModel: ObservableObject {
//  @Published public private(set) var state: CardPreviewState = .loading
//
//  @Published public var cardURL: URL?
//  @Published public var artist: String
//  @Published public var song: String
//  @Published public var imageURL: URL?
//  @Published public var errorMessage: String?
//
//  public init(artist: String, song: String) {
//    self.artist = artist
//    self.song = song
//  }
//
//  public func updateState(_ imageURL: URL?) {
//    if imageURL == nil {
//      state = .empty
//    } else {
//      state = .content(imageURL!)
//    }
//  }
//
//  public func fetchCardPreview() {
//    state = .loading
//    print("Getting Card Preview...")
//    imageURL = URL(string: "https://placehold.co/648x1011")
//    updateState(imageURL)
//  }
// }
