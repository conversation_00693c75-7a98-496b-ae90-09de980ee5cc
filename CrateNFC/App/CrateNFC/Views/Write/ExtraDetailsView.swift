import SwiftUI

struct ExtraDetailsView: View {
  @StateObject private var viewModel: ExtraDetailsViewModel

  init(
    artist: String,
    song: String,
    created: String,
    url: String,
    onDetailsChanged: ((Bool, String) -> Void)? = nil
  ) {
    _viewModel = StateObject(wrappedValue: ExtraDetailsViewModel(
      url: url,
      artist: artist,
      song: song,
      created: created,
      onDetailsChanged: onDetailsChanged
    ))
  }

  var body: some View {
    CrateViewComponent {
      NavigationStack {
        getMainContent()
      }
    }
  }

  @ViewBuilder
  private func getMainContent() -> some View {
    ScrollView {
      VStack(alignment: .center) {
        getTitleSection()
        getToggleSection()
        getDetailsSection()
      }
      .padding()
    }
  }

  @ViewBuilder
  private func getTitleSection() -> some View {
    Text("Extra Details")
      .bold()
      .foregroundColor(Color.primary)
      .padding(.bottom, 10)
  }

  @ViewBuilder
  private func getToggleSection() -> some View {
    Toggle("Write Extra Details", isOn: $viewModel.showExtraDetails)
      .tint(.accentColor)
      .padding(.horizontal)
      .padding(.bottom, 10)
      .onChange(of: viewModel.showExtraDetails) { _, _ in
        viewModel.updateParent()
      }
  }

  @ViewBuilder
  private func getDetailsSection() -> some View {
    VStack(spacing: 10) {
      CrateDetails(details: viewModel.visibleDetails)
    }
    .opacity(viewModel.showExtraDetails ? 0.6 : 1.0)
  }
}

// MARK: - Preview

#Preview {
  ExtraDetailsView(
    artist: "Artist Name",
    song: "Song Title",
    created: "2024-12-03",
    url: "http://example.com"
  )
}
