import Foundation

@MainActor
final class ExtraDetailsViewModel: ObservableObject {
  @Published var showExtraDetails: Bool {
    didSet {
      UserDefaults.standard.showExtraDetails = showExtraDetails
    }
  }

  @Published var artist: String
  @Published var song: String
  @Published var creator: String = "lilrobo"
  @Published var created: String
  @Published var url: String
  var onDetailsChanged: ((Bool, String) -> Void)?

  var visibleDetails: [(title: String, content: String, isUrl: Bool)] {
    if showExtraDetails {
      return [
        ("URL", url, true),
        ("Artist", artist, false),
        ("Song", song, false),
        ("Creator", creator, false),
        ("Created", created, false)
      ]
    } else {
      return [
        ("URL", url, true),
        ("Created", created, false)
      ]
    }
  }

  init(
    url: String,
    artist: String,
    song: String,
    created: String,
    onDetailsChanged: ((Bool, String) -> Void)? = nil
  ) {
    showExtraDetails = UserDefaults.standard.showExtraDetails
    self.artist = artist
    self.song = song
    self.created = created
    self.url = url
    self.onDetailsChanged = onDetailsChanged
  }

  func updateParent() {
    onDetailsChanged?(showExtraDetails, creator)
  }
}
