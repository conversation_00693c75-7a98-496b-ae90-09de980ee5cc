import SwiftUI

struct LaunchView: View {
  @State private var isNavViewActive = true
  @EnvironmentObject var deepLinkHandler: DeepLinkHandler

  var body: some View {
    ZStack {
      if isNavViewActive {
        NavView()
          .environmentObject(deepLinkHandler)
      } else {
        Image("launch")
          .resizable()
          .scaledToFit()
          .frame(width: 300, height: 300)
      }
    }
    .onAppear {
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.0) {
        withAnimation {
          isNavViewActive = true
        }
      }
    }
  }
}

#Preview {
  LaunchView()
    .environmentObject(DeepLinkHandler())
}
