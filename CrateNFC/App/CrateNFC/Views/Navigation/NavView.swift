import SwiftData
import SwiftUI

struct NavView: View {
  enum Tab {
    case trending
    // case cardDesign
    case write
    case cardDetails
    case profile
  }

  @State private var selectedTab: Tab = .write
  @EnvironmentObject var deepLinkHandler: DeepLinkHandler
  var body: some View {
    TabView(selection: $selectedTab) {
      TrendingView(selectedTab: $selectedTab, deepLinkHandler: deepLinkHandler)
        .tabItem {
          Image(systemName: "chart.line.uptrend.xyaxis")
          Text("Trending")
        }
        .tag(Tab.trending)

      //      CardDesignView(selectedTab: $selectedTab, deepLinkHandler: deepLinkHandler)
      //        .tabItem {
      //          Image(systemName: "rectangle.portrait.on.rectangle.portrait.angled")
      //          Text("Designs")
      //        }
      //        .tag(Tab.cardDesign)

      WriteView(selectedTab: $selectedTab, deepLinkHandler: deepLinkHandler)
        .tabItem {
          Image(systemName: "platter.filled.top.and.arrow.up.iphone")
          Text("Write")
        }
        .tag(Tab.write)

      CardDetailsView(selectedTab: $selectedTab, deepLinkHandler: deepLinkHandler)
        .tabItem {
          Image(systemName: "doc.richtext")
          Text("Details")
        }
        .tag(Tab.cardDetails)

      ProfileView(selectedTab: $selectedTab, deepLinkHandler: deepLinkHandler)
        .tabItem {
          Image(systemName: "person")
          Text("Profile")
        }
        .tag(Tab.profile)
    }
    .onAppear {
      let appearance = UITabBarAppearance()
      appearance.configureWithTransparentBackground()
      appearance.backgroundColor = UIColor.systemBackground
      appearance.shadowColor = UIColor.gray
      UITabBar.appearance().scrollEdgeAppearance = appearance
      UITabBar.appearance().standardAppearance = appearance
    }
    .overlay(Rectangle().stroke(Color.gray.opacity(0.2), lineWidth: 0.5).ignoresSafeArea())
    .onReceive(deepLinkHandler.$currentTab) { newTab in
      selectedTab = newTab
    }
  }
}
