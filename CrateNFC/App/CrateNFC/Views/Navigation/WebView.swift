import SafariServices
import SwiftUI

struct WebView: View {
  let url: URL

  var body: some View {
    SafariView(url: url)
      .edgesIgnoringSafeArea(.all)
  }
}

struct SafariView: UIViewControllerRepresentable {
  let url: URL

  func makeUIViewController(context _: Context) -> SFSafariViewController {
    let safariVC = SFSafariViewController(url: url)
    return safariVC
  }

  func updateUIViewController(_: SFSafariViewController, context _: Context) { }
}
