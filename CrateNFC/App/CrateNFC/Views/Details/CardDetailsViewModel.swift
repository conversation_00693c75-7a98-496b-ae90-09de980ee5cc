import CrateServices
import SwiftUI

@MainActor
public final class CardDetailsViewModel: ObservableObject {
  @Published public var primaryURL: String = ""
  @Published public var song: String = ""
  @Published public var artist: String = ""
  @Published public var creator: String = ""
  @Published public var created: String = ""
  @Published public var nfcData: String = "No data read yet."

  @Published public var nfcReader = NFCReader()

  public func clearState() {
    primaryURL = ""
    song = ""
    artist = ""
    creator = ""
    created = ""
    nfcData = "No data read yet."
    print("State cleared.")
  }

  public func receiveNFCData(newValue: NFCPayload?) {
    guard let nfcValue = newValue else { return }
    primaryURL = nfcValue.url
    if let metadata = nfcValue.metadata {
      artist = metadata.artist ?? ""
      song = metadata.song ?? ""
      creator = metadata.creator ?? ""
      created = metadata.created
    } else {
      artist = ""
      song = ""
      creator = ""
      created = ""
    }
  }
}
