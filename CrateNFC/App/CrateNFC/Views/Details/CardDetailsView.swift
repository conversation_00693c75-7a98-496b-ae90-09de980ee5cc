import CoreNFC
import SwiftData
import SwiftUI
import CrateServices

struct CardDetailsView: View {
  @StateObject private var viewModel = CardDetailsViewModel()
  @Binding private var selectedTab: NavView.Tab
  private let deepLinkHandler: DeepLinkHandler

  public init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
    self._selectedTab = selectedTab
    self.deepLinkHandler = deepLinkHandler
  }

  var body: some View {
    CrateViewComponent {
      NavigationStack {
        mainContent
      }
    }
  }

  private var mainContent: some View {
    ScrollView {
      VStack(alignment: .center) {
        getTitleSection()
        getButtonSection()
        Spacer()
        getDetailsSection()
      }
      .padding()
    }
    .onReceive(viewModel.nfcReader.$metadata) { newValue in
      viewModel.receiveNFCData(newValue: newValue)
    }
  }

  private func getTitleSection() -> some View {
    Text("Details")
      .bold()
      .foregroundColor(Color.primary)
      .padding(.bottom, 10)
  }

  private func getDetailsSection() -> some View {
    VStack(spacing: 10) {
      CrateDetails(details: [
        ("Primary URL", viewModel.primaryURL, true),
        ("Song", viewModel.song, false),
        ("Artist", viewModel.artist, false),
        ("Creator", viewModel.creator, false),
        ("Created", viewModel.created, false)
      ])
      if viewModel.primaryURL.isEmpty && viewModel.song.isEmpty {
        getPlaceholderView()
      }
    }
  }

  private func getPlaceholderView() -> some View {
    Text("Scan to display card contents here...")
      .foregroundColor(Color.primary)
      .multilineTextAlignment(.center)
      .padding()
      .frame(maxWidth: .infinity)
      .cornerRadius(12)
  }

  private func getButtonSection() -> some View {
    HStack(spacing: 8) {
      CrateButtonComponent(
        title: "Scan NFC Card",
        image: "wave.3.right",
        action: { viewModel.nfcReader.read() },
        textColor: Color.primary,
        outlineColor: Color.primary
      )
      .frame(maxWidth: .infinity)

      CrateButtonComponent(
        title: "",
        image: "xmark",
        action: viewModel.clearState,
        textColor: Color.primary,
        outlineColor: Color.primary
      )
      .frame(width: UIScreen.main.bounds.width / 8)
    }
    .frame(maxWidth: .infinity)
    .frame(height: 50)
    .padding(.horizontal, 28)
  }
}

struct CardDetailsView_Previews: PreviewProvider {
  static var previews: some View {
    Group {
      CardDetailsView(
        selectedTab: .constant(.cardDetails),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone SE (3rd generation)")
      CardDetailsView(
        selectedTab: .constant(.cardDetails),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone 15 Pro")
      CardDetailsView(
        selectedTab: .constant(.cardDetails),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone 15 Pro Max")
    }
  }
}
