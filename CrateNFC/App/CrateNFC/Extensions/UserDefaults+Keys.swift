import Foundation

extension UserDefaults {
  enum Keys {
    static let serverURL = "serverURL"
    static let preferredOS = "preferredOS"
    static let streamingAppDefault = "streamingAppDefault"
    static let showExtraDetails = "showExtraDetails"
  }

  var serverURL: String? {
    get { string(forKey: Keys.serverURL) }
    set { set(newValue, forKey: Keys.serverURL) }
  }

  var preferredOS: String? {
    get { string(forKey: Keys.preferredOS) }
    set { set(newValue, forKey: Keys.preferredOS) }
  }

  var streamingAppDefault: String? {
    get { string(forKey: Keys.streamingAppDefault) }
    set { set(newValue, forKey: Keys.streamingAppDefault) }
  }

  var showExtraDetails: Bool {
    get { bool(forKey: Keys.showExtraDetails) }
    set { set(newValue, forKey: Keys.showExtraDetails) }
  }
}
