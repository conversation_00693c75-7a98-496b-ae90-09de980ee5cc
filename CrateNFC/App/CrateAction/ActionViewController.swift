import CoreServices
import Social
import UIKit
import UniformTypeIdentifiers

class ActionViewController: UIViewController {
  // MARK: - Properties

  // Identifiers for supported types
  private let typeText: String = UTType.text.identifier
  private let typeURL: String = UTType.url.identifier

  // App URL scheme and UserDefaults group name
  private let appURLScheme: String = "CrateNFC"
  private let groupName: String = "group.com.lilrobo.CrateNFCAppGroup"

  // Default key for saving URL in UserDefaults
  private let urlDefaultName: String = "incomingURL"

  // Counter to track completed item processing
  private var itemsProcessed = 0

  // MARK: - View Lifecycle

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    print("🔵 ActionViewController: viewDidAppear started")

    guard let extensionItem = extensionContext?.inputItems.first as? NSExtensionItem else {
      print("❌ ActionViewController: No valid extension item found")
      extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
      return
    }

    guard let attachments = extensionItem.attachments else {
      print("❌ ActionViewController: No attachments found")
      extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
      return
    }

    print("✅ ActionViewController: Found \(attachments.count) attachment(s)")

    for (index, itemProvider) in attachments.enumerated() {
      print("📦 Processing itemProvider \(index + 1)/\(attachments.count)")
      print("📦 Registered Types: \(itemProvider.registeredTypeIdentifiers)")

      if itemProvider.hasItemConformingToTypeIdentifier(typeText) {
        print("🔤 Handling text type")
        handleIncomingText(itemProvider: itemProvider)
      } else if itemProvider.hasItemConformingToTypeIdentifier(typeURL) {
        print("🔗 Handling URL type")
        handleIncomingURL(itemProvider: itemProvider)
      } else {
        print("❌ Unsupported type in itemProvider \(index + 1)")
      }
    }
  }

  // MARK: - Handle Shared Text

  private func handleIncomingText(itemProvider: NSItemProvider) {
    itemProvider.loadItem(forTypeIdentifier: typeText, options: nil) { item, error in
      defer { self.markItemProcessed() }

      if let error {
        print("❌ Text Loading Error: \(error.localizedDescription)")
        return
      }

      print("📥 Incoming item: \(String(describing: item))")
      print("📦 Item class: \(String(describing: type(of: item)))")

      var text: String?
      if let itemString = item as? String {
        print("✅ Received text directly as String")
        text = itemString
      } else if let itemData = item as? Data, let itemString = String(data: itemData, encoding: .utf8) {
        print("✅ Received text as Data, converted to String")
        text = itemString
      } else {
        print("❌ Unsupported item type for text")
      }

      if let text {
        print("📋 Extracted text: \(text)")

        do {
          let detector = try NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue)
          let matches = detector.matches(in: text, options: [], range: NSRange(location: 0, length: text.utf16.count))

          for match in matches {
            if let range = Range(match.range, in: text) {
              let urlString = String(text[range])
              print("🔗 Detected URL: \(urlString)")

              if let encodedURLString = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
                 let appURL = URL(string: "\(self.appURLScheme)://handle?url=\(encodedURLString)") {
                print("🌐 Opening Main App with URL: \(appURL)")
                _ = self.openURL(appURL)
              }
            }
          }
        } catch {
          print("❌ Do-Try Error: \(error.localizedDescription)")
        }
      } else {
        print("❌ Could not extract valid text")
      }
    }
  }

  // MARK: - Handle Shared URL

  private func handleIncomingURL(itemProvider: NSItemProvider) {
    print("🔄 handleIncomingURL: Started loading URL")

    itemProvider.loadItem(forTypeIdentifier: typeURL, options: nil) { item, error in
      defer { self.markItemProcessed() }

      if let error {
        print("❌ URL Loading Error: \(error.localizedDescription)")
        return
      }

      print("📥 Received item of type: \(String(describing: type(of: item)))")

      if let url = item as? NSURL, let urlString = url.absoluteString {
        print("✅ Successfully extracted URL: \(urlString)")

        if let encodedURLString = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
           let appURL = URL(string: "\(self.appURLScheme)://handle?url=\(encodedURLString)") {
          print("🌐 Opening Main App with URL: \(appURL)")
          _ = self.openURL(appURL)
        }
      } else {
        print("❌ Could not retrieve URL from item")
      }
    }
  }

  // MARK: - Mark Processed Items & Complete Extension

  private func markItemProcessed() {
    DispatchQueue.main.async {
      self.itemsProcessed += 1
      if let attachments = self.extensionContext?.inputItems.first as? NSExtensionItem,
         self.itemsProcessed >= attachments.attachments?.count ?? 0 {
        print("✅ All items processed. Completing extension request.")
        self.extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
      }
    }
  }

  // MARK: - Open App URL

  @objc private func openURL(_ url: URL) -> Bool {
    print("🌍 Opening URL: \(url)")
    var responder: UIResponder? = self
    while responder != nil {
      if let application = responder as? UIApplication {
        application.open(url, options: [:]) { success in
          print(success ? "✅ Successfully opened URL" : "❌ Failed to open URL")
        }
        return true
      }
      responder = responder?.next
    }
    print("❌ No UIApplication found in responder chain")
    return false
  }
}
