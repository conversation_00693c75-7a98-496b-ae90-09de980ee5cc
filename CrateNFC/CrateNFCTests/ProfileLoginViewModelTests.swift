import SwiftData
import SwiftUI
import XCTest

@testable import CrateNFC
@testable import CrateServices

@MainActor
final class ProfileLoginViewModelTests: XCTestCase {
  var mockUserService: MockUserService!
  var viewModel: ProfileLoginViewModel!

  override func setUp() {
    super.setUp()
    mockUserService = MockUserService()

    // Create a custom ViewModel with our mock service
    viewModel = ProfileLoginViewModel(
      userService: mockUserService
    )
  }

  override func tearDown() {
    mockUserService = nil
    viewModel = nil
    super.tearDown()
  }

  func testHandleLoginSuccess() async {
    // Given
    mockUserService.shouldSucceed = true
    let testUserDTO = UserDTO(email: "<EMAIL>")
    mockUserService.mockUserDTO = testUserDTO
    mockUserService.mockToken = "test-token"

    viewModel.email = "<EMAIL>"
    viewModel.password = "password123"

    // When
    do {
      let (user, token) = try await viewModel.handleLogin()

      // Then
      XCTAssertTrue(mockUserService.loginCalled)
      XCTAssertEqual(user.email, "<EMAIL>")
      XCTAssertEqual(token, "test-token")
      XCTAssertFalse(viewModel.isLoading)
    } catch {
      XCTFail("Login should succeed, but failed with error: \(error)")
    }
  }

  func testHandleLoginFailure() async {
    // Given
    mockUserService.shouldSucceed = false
    viewModel.email = "<EMAIL>"
    viewModel.password = "wrongpassword"

    // When/Then
    do {
      _ = try await viewModel.handleLogin()
      XCTFail("Login should fail")
    } catch let error as UserServiceError {
      XCTAssertEqual(error, .loginFailed)
      XCTAssertTrue(mockUserService.loginCalled)
      XCTAssertFalse(viewModel.isLoading)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }
}
