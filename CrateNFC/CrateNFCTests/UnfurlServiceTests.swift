import SwiftSoup
import XCTest

@testable import CrateServices

class UnfurlServiceTests: XCTestCase {
  var unfurlService: UnfurlService!

  override func setUp() {
    super.setUp()
    unfurlService = UnfurlService()
  }

  override func tearDown() {
    unfurlService = nil
    super.tearDown()
  }

  func testUnfurlSpotify() async throws {
    let spotifyHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track Name" />
          <meta property="og:description" content="Test Artist · Album" />
          <meta property="og:image" content="https://example.com/image.jpg" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://open.spotify.com/track/123456")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: spotifyHTML)

    XCTAssertEqual(contentDTO.title, "Test Track Name")
    XCTAssertEqual(contentDTO.detail, "Test Artist")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "open.spotify.com")
  }

  func testUnfurlAppleMusic() async throws {
    let appleMusicHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track Name by Test Artist on Apple Music" />
          <meta property="twitter:image" content="https://example.com/image.jpg" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://music.apple.com/track/123456")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: appleMusicHTML)

    XCTAssertEqual(contentDTO.title, "Test Track Name")
    XCTAssertEqual(contentDTO.detail, "Test Artist")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "music.apple.com")
  }

  func testUnfurlSoundXYZ() async throws {
    let soundXYZHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Artist - Test Track Name" />
          <meta property="og:image" content="https://example.com/image.jpg" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://sound.xyz/track/123456")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: soundXYZHTML)

    XCTAssertEqual(contentDTO.title, "Test Track Name")
    XCTAssertEqual(contentDTO.detail, "Test Artist")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "sound.xyz")
  }

  func testUnfurlPandora() async throws {
    let pandoraHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track Name" />
          <meta property="og:image" content="https://example.com/image.jpg" />
          <meta name="description" content="Listen to Test Artist on Pandora - Lorem ipsum..." />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://pandora.com/track/123456")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: pandoraHTML)

    XCTAssertEqual(contentDTO.title, "Test Track Name")
    XCTAssertEqual(contentDTO.detail, "Test Artist")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "pandora.com")
  }

  func testUnfurlSoundcloud() async throws {
    let soundcloudHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track Name" />
          <meta property="og:image" content="https://example.com/image.jpg" />
          <meta name="description" content="Stream Test Track Name by Test Artist on SoundCloud" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://soundcloud.com/track/123456")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: soundcloudHTML)

    XCTAssertEqual(contentDTO.title, "Test Track Name")
    XCTAssertEqual(contentDTO.detail, "Test Artist")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "soundcloud.com")
  }

  func testUnfurlSpinamp() async throws {
    let spinampHTML = """
      <html>
      <head>
          <meta property="og:image" content="https://example.com/image.jpg" />
      </head>
      <body></body>
      </html>
      """

    // Note the URL path format that matches the regex pattern: /track/song-name-123
    let url = URL(string: "https://spinamp.xyz/track/test-song-123")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: spinampHTML)

    XCTAssertEqual(contentDTO.title, "Test Song")
    XCTAssertEqual(contentDTO.detail, "Spinamp")
    XCTAssertEqual(contentDTO.mediaUrl, "https://example.com/image.jpg")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "spinamp.xyz")
  }

  func testUnfurlYoutube() async throws {
    let youtubeHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track" />
          <meta name="title" content="Test Artist - Test Track" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://youtube.com/watch?v=dQw4w9WgXcQ")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: youtubeHTML)

    XCTAssertEqual(contentDTO.mediaUrl, "https://www.youtube.com/img/desktop/yt_1200.png")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "youtube.com")

    // Since YouTube parsing is complex, we might get nil values in the test environment
    // but the key is that the unfurl function completes without errors
  }

  func testUnfurlShortYoutube() async throws {
    let youtubeHTML = """
      <html>
      <head>
          <meta property="og:title" content="Test Track" />
          <meta name="title" content="Test Artist - Test Track" />
      </head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://youtu.be/dQw4w9WgXcQ")!
    let contentDTO = try await unfurlWithMockedHTML(url: url, html: youtubeHTML)

    XCTAssertEqual(contentDTO.mediaUrl, "https://www.youtube.com/img/desktop/yt_1200.png")
    XCTAssertEqual(URL(string: contentDTO.url ?? "")?.host, "youtu.be")
  }

  func testUnfurlWithMissingOGTags() async {
    let emptyHTML = """
      <html>
      <head></head>
      <body></body>
      </html>
      """

    let url = URL(string: "https://example.com/track/123456")!

    do {
      _ = try await unfurlWithMockedHTML(url: url, html: emptyHTML)
      XCTFail("Expected to throw an error")
    } catch let error as UnfurlError {
      XCTAssertEqual(error, UnfurlError.missingOGTags)
    } catch {
      XCTFail("Unexpected error: \(error)")
    }
  }

  private func unfurlWithMockedHTML(url: URL, html: String) async throws -> ContentDTO {
    class MockURLProtocol: URLProtocol {
      static var mockHTML: String?

      override class func canInit(with _: URLRequest) -> Bool {
        return true
      }

      override class func canonicalRequest(for request: URLRequest) -> URLRequest {
        return request
      }

      override func startLoading() {
        guard let html = MockURLProtocol.mockHTML,
              let data = html.data(using: .utf8)
        else {
          client?.urlProtocolDidFinishLoading(self)
          return
        }

        let response = HTTPURLResponse(
          url: request.url!,
          statusCode: 200,
          httpVersion: nil,
          headerFields: nil
        )!

        client?.urlProtocol(self, didReceive: response, cacheStoragePolicy: .notAllowed)
        client?.urlProtocol(self, didLoad: data)
        client?.urlProtocolDidFinishLoading(self)
      }

      override func stopLoading() {}
    }

    let config = URLSessionConfiguration.ephemeral
    config.protocolClasses = [MockURLProtocol.self]

    MockURLProtocol.mockHTML = html

    _ = URLSession(configuration: config)

    class TestUnfurlService: UnfurlService {
      private let mockHTML: String

      init(mockHTML: String) {
        self.mockHTML = mockHTML
        super.init()
      }

      override func fetchHTML(url _: URL) async throws -> Document {
        return try SwiftSoup.parse(mockHTML)
      }
    }

    let testService = TestUnfurlService(mockHTML: html)
    return try await testService.unfurl(url: url)
  }
}

extension UnfurlError: @retroactive Equatable {
  public static func == (lhs: UnfurlError, rhs: UnfurlError) -> Bool {
    switch (lhs, rhs) {
    case (.invalidHTML, .invalidHTML),
         (.missingOGTags, .missingOGTags):
      return true
    case let (.networkError(lhsError), .networkError(rhsError)):
      return lhsError.localizedDescription == rhsError.localizedDescription
    case let (.other(lhsError), .other(rhsError)):
      return lhsError.localizedDescription == rhsError.localizedDescription
    default:
      return false
    }
  }
}
