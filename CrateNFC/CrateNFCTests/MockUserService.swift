import CrateServices
import Foundation
import MSAL

class MockUserService: UserServiceProtocol {
  var shouldSucceed = true
  var mockUser: User?
  var mockToken = "mock-token"
  var loginCalled = false
  var logoutCalled = false

  var mockUserDTO: UserDTO?
  var mockError: Error?

  func login(email: String, password: String) async throws -> (UserDTO, String) {
    loginCalled = true

    if let error = mockError {
      throw error
    }

    if shouldSucceed {
      let userDTO = mockUserDTO ?? UserDTO(email: email)
      return (userDTO, mockToken)
    } else {
      throw UserServiceError.loginFailed
    }
  }

  func logout() {
    logoutCalled = true
  }

  func getMSALInstance() -> MSALNativeAuthPublicClientApplication? {
    return nil
  }
}
