import Foundation
import SwiftData
import XCTest

@testable import CrateServices

class CollectionServiceTests: XCTestCase {
  // MARK: - Test Properties

  private var mockHTTPClient: MockHTTPClient!
  private var mockUserState: MockUserState!
  private var collectionService: CollectionService!

  override func setUp() {
    super.setUp()
    mockHTTPClient = MockHTTPClient()
    mockUserState = MockUserState()
    collectionService = CollectionService(client: mockHTTPClient, userState: mockUserState)
  }

  override func tearDown() {
    mockHTTPClient = nil
    mockUserState = nil
    collectionService = nil
    super.tearDown()
  }

  // MARK: - Mock UserState

  class MockUserState: UserState {
    var mockIsSignedIn = false
    var mockToken: String?

    override var isSignedIn: Bool {
      return mockIsSignedIn
    }

    override var token: String? {
      get { return mockToken }
      set { mockToken = newValue }
    }
  }

  // MARK: - Test Cases

  func testGetCollectionsSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCollectionsJSON = """
      [
          {
              "id": 1,
              "name": "Test Collection",
              "thumbnail": "https://example.com/thumbnail.jpg",
              "contents": [
                  {
                      "id": 101,
                      "title": "Test Content",
                      "detail": "Test Detail",
                      "url": "https://example.com/content",
                      "created": "2024-01-01T00:00:00Z",
                      "updated": "2024-01-01T00:00:00Z"
                  }
              ],
              "created": "2024-01-01T00:00:00Z",
              "updated": "2024-01-01T00:00:00Z"
          }
      ]
      """
    mockHTTPClient.mockResponse = mockCollectionsJSON.data(using: .utf8)

    // When
    do {
      let collections = try await collectionService.getCollections(start: 0, size: 10)

      // Then
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(collections.count, 1)
      XCTAssertEqual(collections[0].name, "Test Collection")
      XCTAssertEqual(collections[0].contents?.count ?? 0, 1)
      XCTAssertEqual(collections[0].contents?[0].title, "Test Content")
      XCTAssertEqual(collections[0].contents?[0].detail, "Test Detail")
    } catch {
      XCTFail("Get collections should succeed, but failed with error: \(error)")
    }
  }

  func testGetCollectionsWithCustomParameters() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCollectionsJSON = """
      [
          {
              "id": 1,
              "name": "Test Collection",
              "thumbnail": "https://example.com/thumbnail.jpg",
              "contents": [],
              "created": "2024-01-01T00:00:00Z",
              "updated": "2024-01-01T00:00:00Z"
          }
      ]
      """
    mockHTTPClient.mockResponse = mockCollectionsJSON.data(using: .utf8)

    // When
    do {
      let collections = try await collectionService.getCollections(start: 10, size: 5)

      // Then
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(collections.count, 1)
      // The fact that this succeeds means the parameters were handled correctly
    } catch {
      XCTFail(
        "Get collections with custom parameters should succeed, but failed with error: \(error)")
    }
  }

  func testGetCollectionsUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.getCollections()
      XCTFail("Get collections should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }

  func testCreateEmptyCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCreatedCollectionJSON = """
      {
          "id": 2,
          "name": "New Test Collection",
          "thumbnail": "https://example.com/new-thumbnail.jpg",
          "contents": [],
          "created": "2024-01-02T00:00:00Z",
          "updated": "2024-01-02T00:00:00Z"
      }
      """
    mockHTTPClient.mockResponse = mockCreatedCollectionJSON.data(using: .utf8)

    // When
    do {
      let createdCollection = try await collectionService.createEmptyCollection(
        name: "New Test Collection",
        thumbnail: "https://example.com/new-thumbnail.jpg"
      )

      // Then
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(createdCollection.name, "New Test Collection")
      XCTAssertEqual(createdCollection.id, 2)
      XCTAssertEqual(createdCollection.contents.count, 0)

      // Verify request body by parsing the JSON instead of string matching
      if let bodyData = mockHTTPClient.capturedBody {
        do {
          let json = try JSONSerialization.jsonObject(with: bodyData) as? [String: Any]
          XCTAssertEqual(json?["name"] as? String, "New Test Collection")
          XCTAssertEqual(json?["thumbnail"] as? String, "https://example.com/new-thumbnail.jpg")
        } catch {
          XCTFail("Could not parse request body as JSON: \(error)")
        }
      } else {
        XCTFail("No request body was captured")
      }
    } catch {
      XCTFail("Create empty collection should succeed, but failed with error: \(error)")
    }
  }

  func testCreateEmptyCollectionWithoutThumbnail() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCreatedCollectionJSON = """
      {
          "id": 3,
          "name": "Collection Without Thumbnail",
          "thumbnail": "",
          "contents": [],
          "created": "2024-01-02T00:00:00Z",
          "updated": "2024-01-02T00:00:00Z"
      }
      """
    mockHTTPClient.mockResponse = mockCreatedCollectionJSON.data(using: .utf8)

    // When
    do {
      let createdCollection = try await collectionService.createEmptyCollection(
        name: "Collection Without Thumbnail"
      )

      // Then
      XCTAssertEqual(createdCollection.name, "Collection Without Thumbnail")
      XCTAssertEqual(createdCollection.thumbnail, "")
    } catch {
      XCTFail(
        "Create empty collection without thumbnail should succeed, but failed with error: \(error)")
    }
  }

  func testCreateEmptyCollectionUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.createEmptyCollection(name: "Test Collection")
      XCTFail("Create empty collection should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }

  func testAddContentToCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockResponseJSON = """
      {
          "id": 1,
          "name": "Test Collection",
          "thumbnail": "https://example.com/thumbnail.jpg",
          "contents": [
              {
                  "id": 101,
                  "title": "Test Content",
                  "detail": "Test Detail",
                  "url": "https://example.com/content",
                  "created": "2024-01-01T00:00:00Z",
                  "updated": "2024-01-01T00:00:00Z"
              }
          ],
          "created": "2024-01-01T00:00:00Z",
          "updated": "2024-01-01T00:00:00Z"
      }
      """
    mockHTTPClient.mockResponse = mockResponseJSON.data(using: .utf8)

    // When
    do {
      let result = try await collectionService.addContentToCollection(
        collectionServerId: 1,
        contentServerId: 101
      )

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection/1/add")

      // Verify request body contains the content ID
      if let bodyData = mockHTTPClient.capturedBody,
         let bodyString = String(data: bodyData, encoding: .utf8) {
        XCTAssertTrue(bodyString.contains("101"))
      }
    } catch {
      XCTFail("Add content to collection should succeed, but failed with error: \(error)")
    }
  }

  func testRemoveContentFromCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockResponseJSON = """
      {
          "id": 1,
          "name": "Test Collection",
          "thumbnail": "https://example.com/thumbnail.jpg",
          "contents": [],
          "created": "2024-01-01T00:00:00Z",
          "updated": "2024-01-01T00:00:00Z"
      }
      """
    mockHTTPClient.mockResponse = mockResponseJSON.data(using: .utf8)

    // When
    do {
      let result = try await collectionService.removeContentFromCollection(
        collectionServerId: 1,
        contentServerId: 101
      )

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection/1/remove")
    } catch {
      XCTFail("Remove content from collection should succeed, but failed with error: \(error)")
    }
  }

  func testDeleteCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    // DELETE endpoint returns raw Data, so we can use empty data
    mockHTTPClient.mockResponse = Data()

    // When
    do {
      let result = try await collectionService.deleteCollection(serverId: 1)

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockHTTPClient.capturedPath, "/api/v1/collection/1")
    } catch {
      XCTFail("Delete collection should succeed, but failed with error: \(error)")
    }
  }

  func testDeleteCollectionUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.deleteCollection(serverId: 1)
      XCTFail("Delete collection should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }
}
