import XCTest

@testable import CrateNFC

final class PasswordValidatorTests: XCTestCase {

  // MARK: - Basic Validation Tests

  func testValidPasswords() {
    let validPasswords = [
      "Password123!",  // All 4 groups
      "MySecure123",  // 3 groups: upper, lower, numbers
      "password123!",  // 3 groups: lower, numbers, symbols
      "PASSWORD123!",  // 3 groups: upper, numbers, symbols
      "MyPassword!",  // 3 groups: upper, lower, symbols
      "VeryLongPasswordWith123AndSymbols!",  // Long password with all groups
      "Aa1!",  // Minimum length with all groups (but only 4 chars - should fail)
      "Minimum8Aa1!"  // Exactly 8 chars with all groups
    ]

    for password in validPasswords where password.count >= 8 {
      XCTAssertTrue(
        PasswordValidator.isValidEntraPassword(password),
        "Password '\(password)' should be valid"
      )
    }
  }

  func testInvalidPasswords() {
    let invalidPasswords = [
      "",  // Empty
      "short",  // Too short
      "password",  // Only lowercase
      "PASSWORD",  // Only uppercase
      "12345678",  // Only numbers
      "!@#$%^&*",  // Only symbols
      "Password",  // Only 2 groups (upper + lower)
      "password123",  // Only 2 groups (lower + numbers)
      "PASSWORD123",  // Only 2 groups (upper + numbers)
      "password!@#",  // Only 2 groups (lower + symbols)
      "Aa1",  // Too short (3 chars)
      "Aa12345"  // 7 chars (too short)
    ]

    for password in invalidPasswords {
      XCTAssertFalse(
        PasswordValidator.isValidEntraPassword(password),
        "Password '\(password)' should be invalid"
      )
    }
  }

  // MARK: - Length Validation Tests

  func testPasswordLength() {
    // Test minimum length (8 characters)
    let minValidPassword = "Aa1!Aa1!"  // Exactly 8 chars with all groups
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(minValidPassword))

    // Test just under minimum
    let tooShort = "Aa1!Aa1"  // 7 chars
    XCTAssertFalse(PasswordValidator.isValidEntraPassword(tooShort))

    // Test maximum length (256 characters)
    let maxPassword = String(repeating: "Aa1!", count: 64)  // 256 chars
    XCTAssertEqual(maxPassword.count, 256)
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(maxPassword))

    // Test over maximum
    let tooLong = String(repeating: "Aa1!", count: 65)  // 260 chars
    XCTAssertEqual(tooLong.count, 260)
    XCTAssertFalse(PasswordValidator.isValidEntraPassword(tooLong))
  }

  // MARK: - Character Group Tests

  func testCharacterGroups() {
    // Test each individual group
    let result1 = PasswordValidator.validatePasswordRequirements("abcdefgh")  // Only lowercase
    XCTAssertTrue(result1.hasLowercase)
    XCTAssertFalse(result1.hasUppercase)
    XCTAssertFalse(result1.hasNumbers)
    XCTAssertFalse(result1.hasSymbols)
    XCTAssertEqual(result1.characterGroupCount, 1)

    let result2 = PasswordValidator.validatePasswordRequirements("ABCDEFGH")  // Only uppercase
    XCTAssertFalse(result2.hasLowercase)
    XCTAssertTrue(result2.hasUppercase)
    XCTAssertFalse(result2.hasNumbers)
    XCTAssertFalse(result2.hasSymbols)
    XCTAssertEqual(result2.characterGroupCount, 1)

    let result3 = PasswordValidator.validatePasswordRequirements("12345678")  // Only numbers
    XCTAssertFalse(result3.hasLowercase)
    XCTAssertFalse(result3.hasUppercase)
    XCTAssertTrue(result3.hasNumbers)
    XCTAssertFalse(result3.hasSymbols)
    XCTAssertEqual(result3.characterGroupCount, 1)

    let result4 = PasswordValidator.validatePasswordRequirements("!@#$%^&*")  // Only symbols
    XCTAssertFalse(result4.hasLowercase)
    XCTAssertFalse(result4.hasUppercase)
    XCTAssertFalse(result4.hasNumbers)
    XCTAssertTrue(result4.hasSymbols)
    XCTAssertEqual(result4.characterGroupCount, 1)
  }

  func testThreeCharacterGroups() {
    // Test all combinations of 3 groups (should be valid)
    let combinations = [
      "Password123",  // Upper + Lower + Numbers
      "password123!",  // Lower + Numbers + Symbols
      "PASSWORD123!",  // Upper + Numbers + Symbols
      "Password!@#"  // Upper + Lower + Symbols
    ]

    for password in combinations {
      let result = PasswordValidator.validatePasswordRequirements(password)
      XCTAssertEqual(
        result.characterGroupCount, 3, "Password '\(password)' should have 3 character groups")
      XCTAssertTrue(
        result.characterGroupsValid, "Password '\(password)' should have valid character groups")
      XCTAssertTrue(
        PasswordValidator.isValidEntraPassword(password), "Password '\(password)' should be valid")
    }
  }

  func testFourCharacterGroups() {
    let password = "Password123!"
    let result = PasswordValidator.validatePasswordRequirements(password)

    XCTAssertTrue(result.hasUppercase)
    XCTAssertTrue(result.hasLowercase)
    XCTAssertTrue(result.hasNumbers)
    XCTAssertTrue(result.hasSymbols)
    XCTAssertEqual(result.characterGroupCount, 4)
    XCTAssertTrue(result.characterGroupsValid)
    XCTAssertTrue(result.isValid)
  }

  // MARK: - Validation Error Messages Tests

  func testValidationErrorMessages() {
    // Test length error
    let shortPassword = "Aa1!"
    let shortErrors = PasswordValidator.getValidationErrors(shortPassword)
    XCTAssertTrue(shortErrors.contains("Password must be between 8 and 256 characters"))

    // Test character group error
    let weakPassword = "password"  // 8 chars but only 1 group
    let weakErrors = PasswordValidator.getValidationErrors(weakPassword)
    XCTAssertTrue(
      weakErrors.contains {
        $0.contains("Need") && $0.contains("more character groups")
      })

    // Test valid password (no errors)
    let validPassword = "Password123!"
    let validErrors = PasswordValidator.getValidationErrors(validPassword)
    XCTAssertTrue(validErrors.isEmpty)
  }

  // MARK: - Password Requirements Tests

  func testPasswordRequirements() {
    let requirements = PasswordValidator.getPasswordRequirements()

    XCTAssertFalse(requirements.isEmpty)
    XCTAssertTrue(requirements.contains("Be between 8 and 256 characters"))
    XCTAssertTrue(requirements.contains("Contain at least 3 of the following 4 character groups:"))
    XCTAssertTrue(requirements.contains("• Uppercase letters (A–Z)"))
    XCTAssertTrue(requirements.contains("• Lowercase letters (a–z)"))
    XCTAssertTrue(requirements.contains("• Numbers (0–9)"))
    XCTAssertTrue(requirements.contains { $0.contains("• Symbols") })
  }

  // MARK: - Edge Cases Tests

  func testEdgeCases() {
    // Test with Unicode characters
    let unicodePassword = "Pässwörd123!"
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(unicodePassword))

    // Test with spaces
    let passwordWithSpaces = "My Password 123!"
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(passwordWithSpaces))

    // Test with all types of symbols
    let symbolPassword = "Password123!@#$%^&*()-_=+[]{}|;:'\",.<>?/"
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(symbolPassword))

    // Test boundary case - exactly 3 groups
    let boundaryPassword = "password123!"  // lower + numbers + symbols
    let result = PasswordValidator.validatePasswordRequirements(boundaryPassword)
    XCTAssertEqual(result.characterGroupCount, 3)
    XCTAssertTrue(result.characterGroupsValid)
    XCTAssertTrue(PasswordValidator.isValidEntraPassword(boundaryPassword))
  }

  // MARK: - PasswordValidationResult Tests

  func testValidationResultProperties() {
    let password = "Password123!"
    let result = PasswordValidator.validatePasswordRequirements(password)

    // Test present groups
    let presentGroups = result.presentGroups
    XCTAssertEqual(presentGroups.count, 4)
    XCTAssertTrue(presentGroups.contains("Uppercase"))
    XCTAssertTrue(presentGroups.contains("Lowercase"))
    XCTAssertTrue(presentGroups.contains("Numbers"))
    XCTAssertTrue(presentGroups.contains("Symbols"))

    // Test missing groups (should be empty)
    let missingGroups = result.missingGroups
    XCTAssertTrue(missingGroups.isEmpty)
  }

  func testValidationResultMissingGroups() {
    let password = "password"  // Only lowercase
    let result = PasswordValidator.validatePasswordRequirements(password)

    // Test present groups
    let presentGroups = result.presentGroups
    XCTAssertEqual(presentGroups.count, 1)
    XCTAssertTrue(presentGroups.contains("Lowercase"))

    // Test missing groups
    let missingGroups = result.missingGroups
    XCTAssertEqual(missingGroups.count, 3)
    XCTAssertTrue(missingGroups.contains("Uppercase letters"))
    XCTAssertTrue(missingGroups.contains("Numbers"))
    XCTAssertTrue(missingGroups.contains("Symbols"))
  }

  // MARK: - Performance Tests

  func testValidationPerformance() {
    let password = "TestPassword123!"

    measure {
      for _ in 0..<1000 {
        _ = PasswordValidator.isValidEntraPassword(password)
      }
    }
  }

  // MARK: - Real-world Password Tests

  func testRealWorldPasswordExamples() {
    // Test some specific real-world examples
    XCTAssertTrue(PasswordValidator.isValidEntraPassword("MySecureP@ss123"))
    XCTAssertTrue(PasswordValidator.isValidEntraPassword("Welcome2024!"))
    XCTAssertTrue(PasswordValidator.isValidEntraPassword("StrongPassword1"))
    XCTAssertFalse(PasswordValidator.isValidEntraPassword("password"))
    XCTAssertFalse(PasswordValidator.isValidEntraPassword("PASSWORD"))
    XCTAssertFalse(PasswordValidator.isValidEntraPassword("12345678"))
    XCTAssertFalse(PasswordValidator.isValidEntraPassword("short"))
  }

  // MARK: - Specific Symbol Tests

  func testSpecificSymbols() {
    let symbolSets = [
      "Password123!",  // Exclamation
      "Password123@",  // At symbol
      "Password123#",  // Hash
      "Password123$",  // Dollar
      "Password123%",  // Percent
      "Password123^",  // Caret
      "Password123&",  // Ampersand
      "Password123*",  // Asterisk
      "Password123(",  // Parentheses
      "Password123-",  // Hyphen
      "Password123_",  // Underscore
      "Password123=",  // Equals
      "Password123+",  // Plus
      "Password123[",  // Brackets
      "Password123{",  // Braces
      "Password123|",  // Pipe
      "Password123;",  // Semicolon
      "Password123:",  // Colon
      "Password123'",  // Apostrophe
      "Password123\"",  // Quote
      "Password123,",  // Comma
      "Password123.",  // Period
      "Password123<",  // Less than
      "Password123>",  // Greater than
      "Password123?",  // Question mark
      "Password123/"  // Forward slash
    ]

    for password in symbolSets {
      XCTAssertTrue(
        PasswordValidator.isValidEntraPassword(password),
        "Password with symbol '\(password.last!)' should be valid"
      )
    }
  }
}
