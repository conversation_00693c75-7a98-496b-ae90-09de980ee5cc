import Foundation
@testable import CrateServices

/// Mock implementation of HTTPClientProtocol for testing purposes.
/// Allows stubbing responses and capturing request details for verification.
public final class MockHTTPClient: HTTPClientProtocol {

  // MARK: - Mock Response Configuration

  /// The mock response data to return for requests
  public var mockResponse: Data?

  /// The mock error to throw for requests
  public var mockError: Error?

  // MARK: - Request Capture Properties

  /// Captures the path of the last request
  public var capturedPath: String?

  /// Captures the body data of the last request
  public var capturedBody: Data?

  /// Captures the parameters of the last request
  public var capturedParameters: Any?

  /// Captures the HTTP method of the last request
  public var capturedMethod: String?

  // MARK: - Configuration Capture Properties

  /// Captures the last bearer token that was set
  public var capturedBearerToken: String?

  /// Captures the last API key that was set
  public var capturedApiKey: String?

  /// Captures the last base URL that was set
  public var capturedBaseURL: String?

  /// Tracks whether the bearer token was removed
  public var bearerTokenRemoved = false

  // MARK: - Initialization

  public init() {}

  // MARK: - HTTPClientProtocol Implementation

  public func get<Parameters: Encodable, Response: Decodable>(
    path: String,
    parameters: Parameters? = nil
  ) async throws -> Response {
    capturedPath = path
    capturedParameters = parameters
    capturedMethod = "GET"

    if let error = mockError {
      throw error
    }

    guard let responseData = mockResponse else {
      throw HTTPError.invalidResponse
    }

    do {
      return try JSONDecoder().decode(Response.self, from: responseData)
    } catch {
      throw HTTPError.decodingError(error)
    }
  }

  public func post<RequestBody: Encodable, Response: Decodable>(
    path: String,
    body: RequestBody? = nil
  ) async throws -> Response {
    capturedPath = path
    capturedMethod = "POST"

    if let requestBody = body {
      capturedBody = try JSONEncoder().encode(requestBody)
    }

    if let error = mockError {
      throw error
    }

    guard let responseData = mockResponse else {
      throw HTTPError.invalidResponse
    }

    do {
      return try JSONDecoder().decode(Response.self, from: responseData)
    } catch {
      throw HTTPError.decodingError(error)
    }
  }

  public func postWithoutResponse<RequestBody: Encodable>(
    path: String,
    body: RequestBody? = nil
  ) async throws {
    capturedPath = path
    capturedMethod = "POST"

    if let requestBody = body {
      capturedBody = try JSONEncoder().encode(requestBody)
    }

    if let error = mockError {
      throw error
    }

    // For postWithoutResponse, we don't need to return anything
    // Just verify we don't have an error
  }

  public func delete<RequestBody: Encodable>(
    path: String,
    body: RequestBody? = nil
  ) async throws -> Data {
    capturedPath = path
    capturedMethod = "DELETE"

    if let requestBody = body {
      capturedBody = try JSONEncoder().encode(requestBody)
    }

    if let error = mockError {
      throw error
    }

    return mockResponse ?? Data()
  }

  public func setBaseURL(_ urlString: String) async throws {
    capturedBaseURL = urlString

    // Validate URL format like the real implementation
    guard URL(string: urlString) != nil else {
      throw HTTPError.invalidURL
    }
  }

  public func setBearerToken(_ token: String) async {
    capturedBearerToken = token
    bearerTokenRemoved = false
  }

  public func removeBearerToken() async {
    capturedBearerToken = nil
    bearerTokenRemoved = true
  }

  public func setApiKey(_ key: String) async {
    capturedApiKey = key
  }

  // MARK: - Test Helper Methods

  /// Resets all captured values and mock configuration
  public func reset() {
    mockResponse = nil
    mockError = nil
    capturedPath = nil
    capturedBody = nil
    capturedParameters = nil
    capturedMethod = nil
    capturedBearerToken = nil
    capturedApiKey = nil
    capturedBaseURL = nil
    bearerTokenRemoved = false
  }

  /// Sets up a successful response with the provided data
  public func setMockResponse<T: Codable>(_ response: T) throws {
    mockResponse = try JSONEncoder().encode(response)
    mockError = nil
  }

  /// Sets up an error response
  public func setMockError(_ error: Error) {
    mockError = error
    mockResponse = nil
  }
}
