#!/bin/zsh

# Find the path to the built .app dynamically
APP_PATH=$(find ~/Library/Developer/Xcode/DerivedData/ -name "CrateNFC.app" | grep -m 1 Development-iphonesimulator)

if [ -z "$APP_PATH" ]; then
  echo "App build not found."
  exit 1
fi

# Define an array of simulators
SIMULATORS=("iPhone 15 Pro" "iPhone 15 Pro Max" "iPhone SE (3rd generation)")

# Function to boot, install, and launch the app on a simulator
install_on_simulator() {
    local simulator_name=$1
    echo "Booting the $simulator_name simulator..."
    xcrun simctl boot "$simulator_name"

    echo "Installing the app on the $simulator_name simulator..."
    xcrun simctl install booted "$APP_PATH"

    echo "Launching the app on the $simulator_name simulator..."
    xcrun simctl launch booted com.lilrobo.cratenfc-app
}

# Loop through each simulator and run the install function in the background
for simulator in "${SIMULATORS[@]}"; do
    install_on_simulator "$simulator" &
done

# Wait for all background processes to finish
wait

echo "All simulators have been set up."
