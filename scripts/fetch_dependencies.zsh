#!/bin/zsh

# this script will execute an error if the Package.swift file is not found
# to make a Package.swift file, run the following command:
# % swift package init --type executable
# this will require us to define our dependencies with version numbers
# we can do this by checking dependencies in xcode

echo "Fetching dependencies..."
swift package resolve

if [ $? -ne 0 ]; then
    echo "Error: Failed to fetch dependencies. \
    Check comments in ./scripts/fetch_dependencies.zsh"
    exit 1
fi

echo "Dependencies fetched successfully."
