#!/bin/zsh

# Define an array of simulators
SIMULATORS=("iPhone 15 Pro" "iPhone 15 Pro Max" "iPhone SE (3rd generation)")

# Function to build the project for a simulator
build_for_simulator() {
    local simulator_name=$1
    echo "Building the project for $simulator_name..."
    xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=$simulator_name" build
}

# Loop through each simulator and run the build function in the background
for simulator in "${SIMULATORS[@]}"; do
    build_for_simulator "$simulator" &
done

# Wait for all background processes to finish
wait

echo "All builds have been completed."
