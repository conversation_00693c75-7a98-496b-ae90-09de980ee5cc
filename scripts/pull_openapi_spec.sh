#!/bin/bash

# Default values
URL="http://192.168.1.64:8000/openapi.yaml"
OUTPUT="./CrateNFC/GeneratedClient/openapi.yaml"
FORMAT="yaml"

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --prod) URL="https://api.cratenfc.com/openapi.yaml";;
        --json) FORMAT="json"; URL="${URL%.yaml}.json";;
        --output) OUTPUT="$2"; shift;;
        *) echo "Unknown parameter passed: $1"; exit 1;;
    esac
    shift
done

# Adjust output path for JSON format
if [[ "$FORMAT" == "json" ]]; then
    OUTPUT="${OUTPUT%.yaml}.json"
fi

# Fetch the OpenAPI spec
curl -o "$OUTPUT" "$URL"

# Check if the curl command was successful
if [[ $? -eq 0 ]]; then
    echo "OpenAPI spec successfully fetched and saved to $OUTPUT"
else
    echo "Failed to fetch OpenAPI spec from $URL"
fi
