#!/bin/zsh

# Define an array of simulators
SIMULATORS=("iPhone 15 Pro" "iPhone 15 Pro Max" "iPhone SE (3rd generation)")

# Function to clean the build folder for a simulator
clean_for_simulator() {
    local simulator_name=$1
    echo "Cleaning the build folder for $simulator_name..."
    xcodebuild -project CrateNFC/CrateNFC.xcodeproj -scheme CrateNFC -configuration Development -destination "platform=iOS Simulator,name=$simulator_name" clean
}

# Loop through each simulator and run the clean function
for simulator in "${SIMULATORS[@]}"; do
    clean_for_simulator "$simulator"
done

echo "All build folders have been cleaned."
