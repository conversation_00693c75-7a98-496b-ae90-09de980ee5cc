import Combine
import Foundation

public struct RawTrackResponse: Codable {
  public let trackTitle: String?
  public let artistName: String?
  public let mediaUrl: String
  public let url: String
  public let domain: String
  public let updatedAt: Date
  public let createdAt: Date

  public enum CodingKeys: String, CodingKey {
    case trackTitle = "track_title"
    case artistName = "artist_name"
    case mediaUrl = "media_url"
    case url, domain
    case updatedAt = "updated_at"
    case createdAt = "created_at"
  }
}

public enum APIError: Error {
  case unexpectedStatusCode(Int)
  case invalidResponse
  case unprocessableEntity
  case unauthorized
}

public class Client {
  public static let shared = Client()

  static let availableServers = [
    ("Development", "http://192.168.1.64:8000"),
    ("Production", "https://api.cratenfc.com")
  ]

  static var defaultServerURL: String {
    availableServers.last?.1 ?? "https://api.cratenfc.com"
  }

  private let session: URLSession
  private(set) var serverURL: URL

  @Published public private(set) var isAuthenticated = false
  private var userDefaultsObserver: NSObjectProtocol?

  public init() {
    let serverURLString = UserDefaults.standard.serverURL

    if let urlString = serverURLString, let url = URL(string: urlString) {
      serverURL = url
    } else {
      serverURL = URL(string: Self.defaultServerURL)!
    }

    session = URLSession.shared

    userDefaultsObserver = NotificationCenter.default.addObserver(
      forName: UserDefaults.didChangeNotification,
      object: nil,
      queue: .main
    ) { [weak self] _ in
      self?.handleServerURLChange()
    }
  }

  deinit {
    if let observer = userDefaultsObserver {
      NotificationCenter.default.removeObserver(observer)
    }
  }

  public func handleServerURLChange() {
    if let newURLString = UserDefaults.standard.serverURL,
       let newURL = URL(string: newURLString),
       newURL != serverURL {
      serverURL = newURL
    }
  }

  public func getUnfurl(url: String) async throws -> UnfurlResponse {
    var request = URLRequest(url: serverURL.appendingPathComponent("unfurl"))
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")

    let body = ["url": url]
    request.httpBody = try JSONEncoder().encode(body)

    let (data, response) = try await session.data(for: request)

    guard let httpResponse = response as? HTTPURLResponse else {
      throw APIError.invalidResponse
    }

    switch httpResponse.statusCode {
    case 200:
      return try JSONDecoder().decode(UnfurlResponse.self, from: data)
    case 422:
      throw APIError.unprocessableEntity
    default:
      throw APIError.unexpectedStatusCode(httpResponse.statusCode)
    }
  }

  public func getTrendingTracks() async throws -> [RawTrackResponse] {
    let request = URLRequest(url: serverURL.appendingPathComponent("trending"))
    let (data, response) = try await session.data(for: request)

    guard let httpResponse = response as? HTTPURLResponse else {
      throw APIError.invalidResponse
    }

    switch httpResponse.statusCode {
    case 200:
      return try JSONDecoder().decode([RawTrackResponse].self, from: data)
    case 422:
      throw APIError.unprocessableEntity
    default:
      throw APIError.unexpectedStatusCode(httpResponse.statusCode)
    }
  }

  public struct UnfurlResponse: Codable {
    public let title: String
    public let artist: String
    public let mediaUrl: String

    public enum CodingKeys: String, CodingKey {
      case title, artist
      case mediaUrl = "media_url"
    }
  }
}
