# Microsoft Entra External ID Authentication Implementation Plan

## Overview

This document outlines the plan for implementing Microsoft Entra External ID authentication using the Microsoft Authentication Library (MSAL) in the CrateNFC iOS app. The implementation will replace the existing authentication system with Microsoft's identity platform, providing a more secure and standardized authentication experience.

## Configuration Details

- **Client ID**: `6d030d81-5d4a-42d1-a4c8-76c03b13fcc4`
- **Tenant Subdomain**: `cratenfc`
- **Protected API URL**: `http://192.168.1.64:8000/api/v1/user`
- **Required Scopes**: `["api://cratenfc/access"]`

## Implementation Steps

### 1. Add MSAL Dependencies

#### Why?
MSAL provides a robust, secure framework for implementing Microsoft authentication. It handles token acquisition, caching, and refresh operations, as well as providing a consistent authentication experience across Microsoft services.

#### Tasks:
- [ ] Add the MSAL Swift package to the project in Xcode
  ```
  https://github.com/AzureAD/microsoft-authentication-library-for-objc
  ```
- [ ] Add a keychain group to your project Capabilities
  - Use `com.microsoft.adalcache` for iOS

- [ ] Configure the Info.plist for URL scheme redirection to handle authentication callbacks
  ```xml
  <key>CFBundleURLTypes</key>
  <array>
      <dict>
          <key>CFBundleURLSchemes</key>
          <array>
              <string>msauth.com.lilrobo.CrateNFC</string>
          </array>
      </dict>
  </array>
  <key>LSApplicationQueriesSchemes</key>
  <array>
      <string>msauthv2</string>
      <string>msauthv3</string>
  </array>
  ```

### 2. Create MSAL Authentication Service

#### Why?
A dedicated service class will encapsulate all MSAL-specific functionality, making it easier to maintain and update. This separation of concerns keeps the authentication logic isolated from the rest of the application.

#### Tasks:
- [ ] Create a new `MSALAuthService.swift` class to handle Microsoft authentication
- [ ] Implement methods for:
  - [ ] Initializing MSALNativeAuthPublicClientApplication with the correct configuration
  - [ ] Setting up challenge types [.OOB, .password] for email and password authentication
  - [ ] Configuring logging for debugging
  - [ ] Signing in users interactively
  - [ ] Acquiring tokens silently (for refresh)
  - [ ] Signing out users
  - [ ] Retrieving user information from MSAL accounts

### 3. Update User Model and UserState

#### Why?
The existing User model and UserState need to be extended to support MSAL-specific attributes and behaviors. This ensures that the app can properly store and manage user information obtained from Microsoft's identity platform.

#### Tasks:
- [ ] Extend the User model to include additional fields for MSAL:
  - [ ] `displayName`: For the user's display name from Microsoft
  - [ ] `msalIdentifier`: To store the MSAL account identifier
- [ ] Update UserState to handle MSAL authentication:
  - [ ] Modify login method to store MSAL tokens
  - [ ] Update logout to properly sign out from MSAL
  - [ ] Ensure token refresh is handled correctly

### 4. Create Authentication Coordinator

#### Why?
An authentication coordinator will manage the flow between the UI, the MSAL service, and the app's state. This coordinator pattern simplifies the integration of MSAL into the existing app architecture.

#### Tasks:
- [ ] Implement a coordinator class to manage the authentication flow
- [ ] Handle redirect URLs and authentication callbacks
- [ ] Coordinate between the UI, MSAL service, and user state

### 5. Update HTTPClient for MSAL Token Management

#### Why?
The existing HTTPClient needs to be updated to properly handle MSAL tokens, including automatic token refresh when tokens expire. This ensures a seamless authentication experience for users.

#### Tasks:
- [ ] Modify the HTTPClient to use MSAL tokens for API requests
- [ ] Implement token refresh logic using MSALSilentTokenParameters for automatic refresh
- [ ] Update error handling for authentication failures

### 6. Update UI Components

#### Why?
The user interface needs to be updated to replace the existing authentication UI with Microsoft authentication. This provides a consistent, secure login experience using Microsoft's identity platform.

#### Tasks:
- [ ] Create a Microsoft login button component
- [ ] Replace existing login/registration views with Microsoft authentication
- [ ] Update view models to handle Microsoft authentication flow
- [ ] Remove legacy authentication UI components

### 7. Implement Protected API Access

#### Why?
The app needs to properly access protected APIs using the MSAL tokens. This ensures that authenticated users can access the resources they're authorized for.

#### Tasks:
- [ ] Update API service to use MSAL tokens for protected endpoints
- [ ] Implement proper scope handling for API requests
- [ ] Ensure token refresh is handled correctly for API calls

## Code Implementation Details

### MSALAuthService.swift

```swift
import MSAL
import Foundation
import Combine

public enum MSALAuthError: Error {
    case configurationError
    case signInError(Error)
    case noAccount
    case tokenAcquisitionError(Error)
    case userCanceled
}

public class MSALAuthService {
    // MSAL configuration
    private let clientId = "6d030d81-5d4a-42d1-a4c8-76c03b13fcc4"
    private let tenantSubdomain = "cratenfc"
    private let authorityURLString = "https://cratenfc.ciamlogin.com"
    private let redirectUri = "msauth.com.lilrobo.CrateNFC://auth"

    // API scopes
    private let apiScopes = ["api://cratenfc/access"]

    // MSAL application instance
    private var nativeAuth: MSALNativeAuthPublicClientApplication?

    // Singleton instance
    public static let shared = MSALAuthService()

    private init() {
        setupMSAL()
        setupLogging()
    }

    // Setup MSAL configuration
    private func setupMSAL() {
        do {
            nativeAuth = try MSALNativeAuthPublicClientApplication(
                clientId: clientId,
                tenantSubdomain: tenantSubdomain,
                challengeTypes: [.OOB, .password] // For email and password authentication
            )
            print("Initialized Native Auth successfully.")
        } catch {
            print("Unable to initialize MSAL: \(error)")
        }
    }

    // Setup logging for debugging
    private func setupLogging() {
        MSALGlobalConfig.loggerConfig.logLevel = .verbose
        MSALGlobalConfig.loggerConfig.setLogCallback { logLevel, message, containsPII in
            if !containsPII {
                print("MSAL: \(message ?? "")")
            }
        }
    }

    // Sign in with MSAL
    public func signIn(from viewController: UIViewController) async throws -> MSALAccount {
        // Implementation details...
    }

    // Get current account
    public func getCurrentAccount() async throws -> MSALAccount {
        // Implementation details...
    }

    // Acquire token silently (uses refresh token automatically)
    public func acquireTokenSilently() async throws -> String {
        do {
            let account = try await getCurrentAccount()
            let parameters = MSALSilentTokenParameters(scopes: apiScopes, account: account)
            let result = try await nativeAuth?.acquireTokenSilent(with: parameters)
            return result?.accessToken ?? ""
        } catch {
            throw MSALAuthError.tokenAcquisitionError(error)
        }
    }

    // Sign out
    public func signOut() async throws {
        // Implementation details...
    }

    // Get user info from account
    public func getUserInfo(from account: MSALAccount) -> (String, String) {
        // Implementation details...
    }
}
```

### User Model Extension

```swift
@Model
public final class User: Sendable {
    public var email: String?
    public var username: String?
    public var displayName: String?  // Add for MSAL display name
    public var msalIdentifier: String?  // Add for MSAL account identifier
    public var updatedAt: Date?
    public var createdAt: Date?

    // Updated initializer...
}
```

## Next Steps and Roadmap

1. **Implement Core Authentication**
   - [ ] Add MSAL dependencies
   - [ ] Create basic authentication service
   - [ ] Update models and state management

2. **Enhance User Experience**
   - [ ] Improve UI for authentication flows
   - [ ] Add proper error handling and user feedback
   - [ ] Implement smooth sign-in/sign-out transitions

3. **Security Enhancements**
   - [ ] Implement secure token storage
   - [ ] Add additional security measures
   - [ ] Optimize token refresh handling

4. **Testing and Validation**
   - [ ] Test authentication flows
   - [ ] Validate token handling
   - [ ] Ensure proper error recovery

5. **Migration Strategy**
   - [ ] Plan for migrating existing users to MSAL
   - [ ] Implement data migration if needed
   - [ ] Consider a phased rollout approach

## Conclusion

This implementation plan provides a structured approach to replacing the existing authentication system with Microsoft Entra External ID authentication using MSAL in the CrateNFC iOS app. By following this plan, the app will gain robust, secure authentication with Microsoft's identity platform, providing a more standardized and maintainable authentication solution. The migration to MSAL-only authentication will simplify the codebase by removing the need to maintain multiple authentication systems while offering users a seamless and secure login experience.
