import Foundation

public actor AccessTokenManager {
  public private(set) var accessToken: String?
  public private(set) var refreshToken: String?

  private let tokenKey = "access_token"
  private let refreshTokenKey = "refresh_token"

  public init(accessToken: String? = nil) {
    self.accessToken = accessToken
  }

  public func updateAccessToken(_ token: String) {
    accessToken = token
  }

  public func saveTokens(accessToken: String, refreshToken: String) {
    self.accessToken = accessToken
    self.refreshToken = refreshToken
    UserDefaults.standard.set(accessToken, forKey: tokenKey)
    UserDefaults.standard.set(refreshToken, forKey: refreshTokenKey)
  }

  public func clearTokens() {
    accessToken = nil
    refreshToken = nil
    UserDefaults.standard.removeObject(forKey: tokenKey)
    UserDefaults.standard.removeObject(forKey: refreshTokenKey)
  }

  private func authHeaders() -> String {
    guard let token = accessToken else { return "" }
    return "Authorization: Bearer \(token)"
  }
}
