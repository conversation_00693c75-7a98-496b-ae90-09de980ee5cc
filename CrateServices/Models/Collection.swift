import Foundation
import SwiftData

@Model
public final class Collection {
  public var serverId: Int?
  public var name: String?
  public var thumbnail: String?
  public var content: [Content]
  public var createdAt: Date?
  public var updatedAt: Date?

  public init(
    serverId: Int? = nil,
    name: String?,
    content: [Content] = [],
    thumbnail: String? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.content = content
    self.thumbnail = thumbnail
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  public func toDTO() -> CollectionDTO {
    CollectionDTO(
      serverId: serverId,
      name: name,
      thumbnail: thumbnail,
      contents: content.map { $0.toDTO() },
      createdAt: createdAt,
      updatedAt: updatedAt
    )
  }
}

public final class CollectionDTO: Sendable, Identifiable {
  public let serverId: Int?
  public let name: String?
  public let thumbnail: String?
  public let contents: [ContentDTO]?
  public let createdAt: Date?
  public let updatedAt: Date?

  public init(
    serverId: Int? = nil,
    name: String? = nil,
    thumbnail: String? = nil,
    contents: [ContentDTO]? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.thumbnail = thumbnail
    self.contents = contents
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  public func toModel() -> Collection {
    Collection(
      serverId: serverId,
      name: name,
      content: contents?.map { $0.toModel() } ?? [],
      thumbnail: thumbnail,
      createdAt: createdAt,
      updatedAt: updatedAt
    )
  }
}
