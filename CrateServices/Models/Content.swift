import Foundation
import SwiftData

@Model
public final class Content: Identifiable {
  public var id: String
  public var serverId: Int?
  public var detail: String?
  public var title: String?
  public var mediaUrl: String?
  public var url: String?
  public var updatedAt: Date?
  public var createdAt: Date?

  public init(
    serverId: Int? = nil,
    detail: String? = nil,
    title: String? = nil,
    mediaUrl: String? = nil,
    url: String? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.id = UUID().uuidString
    self.serverId = serverId
    self.detail = detail
    self.title = title
    self.mediaUrl = mediaUrl
    self.url = url
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }

  public func toDTO() -> ContentDTO {
    ContentDTO(
      serverId: serverId,
      detail: detail,
      title: title,
      mediaUrl: mediaUrl,
      url: url,
      updatedAt: updatedAt,
      createdAt: createdAt
    )
  }
}

public final class ContentDTO: Sendable, Identifiable {
  public let serverId: Int?
  public let detail: String?
  public let title: String?
  public let mediaUrl: String?
  public let url: String?
  public let updatedAt: Date?
  public let createdAt: Date?

  public init(
    serverId: Int? = nil,
    detail: String? = nil,
    title: String? = nil,
    mediaUrl: String? = nil,
    url: String? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.serverId = serverId
    self.detail = detail
    self.title = title
    self.mediaUrl = mediaUrl
    self.url = url
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }

  public func toModel() -> Content {
    Content(
      serverId: serverId,
      detail: detail,
      title: title,
      mediaUrl: mediaUrl,
      url: url,
      updatedAt: updatedAt,
      createdAt: createdAt
    )
  }
}

// Server-side DTO that matches the swagger definition
public struct ServerContentDTO: Codable {
  public let id: Int?
  public let detail: String?
  public let title: String?
  public let mediaUrl: String?
  public let url: String?
  public let created: String?
  public let updated: String?

  public func toContentDTO() -> ContentDTO {
    let isoFormatter = ISO8601DateFormatter()

    return ContentDTO(
      serverId: id,
      detail: detail,
      title: title,
      mediaUrl: mediaUrl,
      url: url,
      updatedAt: updated.flatMap { isoFormatter.date(from: $0) },
      createdAt: created.flatMap { isoFormatter.date(from: $0) }
    )
  }

  public func toContentModel() -> Content {
    let isoFormatter = ISO8601DateFormatter()

    return Content(
      serverId: id,
      detail: detail,
      title: title,
      mediaUrl: mediaUrl,
      url: url,
      updatedAt: updated.flatMap { isoFormatter.date(from: $0) },
      createdAt: created.flatMap { isoFormatter.date(from: $0) }
    )
  }
}
