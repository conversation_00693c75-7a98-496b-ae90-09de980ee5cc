import Foundation
import SwiftData

@Model
public final class RecentCollection {
  public var name: String?
  public var thumbnail: String?
  public var content: [Content]
  public var createdAt: Date?
  public var updatedAt: Date?

  public init(
    name: String?,
    content: [Content] = [],
    thumbnail: String? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.name = name
    self.content = content
    self.thumbnail = thumbnail
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}

public final class RecentCollectionDTO: Sendable, Identifiable {
  public let name: String?
  public let thumbnail: String?
  public let contents: [ContentDTO]?
  public let createdAt: Date?
  public let updatedAt: Date?

  public init(
    name: String? = nil,
    thumbnail: String? = nil,
    contents: [ContentDTO]? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.name = name
    self.thumbnail = thumbnail
    self.contents = contents
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}
