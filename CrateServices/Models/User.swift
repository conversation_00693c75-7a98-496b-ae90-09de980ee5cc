import Foundation
import SwiftData

@Model
public final class User {
  public var email: String?
  public var username: String?
  public var updatedAt: Date?
  public var createdAt: Date?

  public init(
    email: String? = nil,
    username: String? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.email = email
    self.username = username
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }
}

public final class UserDTO: Sendable, Decodable, Identifiable {
  public let email: String?
  public let username: String?
  public let updatedAt: Date?
  public let createdAt: Date?

  public init(
    email: String? = nil,
    username: String? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.email = email
    self.username = username
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }

  public func toModel() -> User {
    return User(
      email: email,
      username: username,
      updatedAt: updatedAt,
      createdAt: createdAt
    )
  }
}
