import CoreNFC
import Foundation

public struct NFCPayload: Codable {
  public let url: String
  public let metadata: NFCMetadata?

  public struct NFCMetadata: Codable {
    public let song: String?
    public let artist: String?
    public let creator: String?
    public let created: String
  }
}

public enum NFCError: Error {
  case invalidPayload
  case invalidFormat
  case missingRecord
}

public class NFCRecordParser {
  func parseNFCMessage(_ message: NFCNDEFMessage) throws -> NFCPayload {
    guard message.records.count >= 1 else {
      print("missing record")
      throw NFCError.missingRecord
    }
    var urlString: String?
    var metadata: NFCPayload.NFCMetadata?

    for record in message.records {
      if let typeString = String(data: record.type, encoding: .utf8) {
        switch typeString {
        case "U":
          guard let payload = String(data: record.payload, encoding: .utf8)?.dropFirst() else {
            throw NFCError.invalidPayload
          }
          print("URL string: \(payload)")
          urlString = String(payload)

        case "T":
          guard let payloadText = String(data: record.payload, encoding: .utf8),
                let jsonData = payloadText.data(using: .utf8) else {
            throw NFCError.invalidPayload
          }

          do {
            metadata = try JSONDecoder().decode(NFCPayload.NFCMetadata.self, from: jsonData)
          } catch {
            print("JSON Decoding error: \(error)")
            throw NFCError.invalidFormat
          }

        default:
          continue
        }
      }
    }

    guard let finalUrl = urlString else {
      print("missing record 2")
      throw NFCError.missingRecord
    }

    let createdDate = metadata?.created ?? ISO8601DateFormatter().string(from: Date())
    let finalMetadata = metadata ?? NFCPayload.NFCMetadata(song: nil, artist: nil, creator: nil, created: createdDate)

    return NFCPayload(url: finalUrl, metadata: finalMetadata)
  }
}
