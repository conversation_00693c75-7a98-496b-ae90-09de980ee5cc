import Combine
import SwiftUI
import SwiftData

public class UserState: ObservableObject {
  @Published public var currentUser: User?
  @AppStorage("userToken") public var token: String?

  public static let shared = UserState()

  private let authPublisher = AuthPublisher.shared

  public var isSignedIn: Bool {
    return currentUser != nil && token != nil
  }

  public func login(user: User, token: String) {
    DispatchQueue.main.async {
      self.currentUser = user
      self.token = token

      // Publish the sign-in event
      self.authPublisher.publishSignIn(user: user)
    }
  }

  public func logout() {
    DispatchQueue.main.async {
      self.currentUser = nil
      self.token = nil

      // Publish the sign-out event
      self.authPublisher.publishSignOut()
    }
  }

  public func updateProfile(user: User) {
    DispatchQueue.main.async {
      self.currentUser = user

      // Publish the profile update event
      self.authPublisher.publishProfileUpdate(user: user)
    }
  }

  public func refreshToken(newToken: String) {
    DispatchQueue.main.async {
      self.token = newToken

      // Publish the token refresh event
      self.authPublisher.publishTokenRefresh(token: newToken)
    }
  }
}
