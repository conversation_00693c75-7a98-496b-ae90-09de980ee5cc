import Foundation
import Combine

public enum AuthEvent: Equatable {
  case signedIn(User)
  case signedOut
  case tokenRefreshed(String)
  case profileUpdated(User)
  case userCreated(User)

  // Implement Equatable manually since associated values make it complex
  public static func == (lhs: AuthEvent, rhs: AuthEvent) -> Bool {
    switch (lhs, rhs) {
    case (.signedIn(let lhsUser), .signedIn(let rhsUser)):
      return lhsUser.id == rhsUser.id
    case (.signedOut, .signedOut):
      return true
    case (.tokenRefreshed(let lhsToken), .tokenRefreshed(let rhsToken)):
      return lhsToken == rhsToken
    case (.profileUpdated(let lhsUser), .profileUpdated(let rhsUser)):
      return lhsUser.id == rhsUser.id
    case (.userCreated(let lhsUser), .userCreated(let rhsUser)):
      return lhsUser.id == rhsUser.id
    default:
      return false
    }
  }
}

public final class AuthPublisher {
  // Singleton instance
  public static let shared = AuthPublisher()

  // The publisher for auth events
  public let publisher = PassthroughSubject<AuthEvent, Never>()

  // Private initializer to enforce singleton pattern
  private init() {}

  // Convenience methods to publish specific events
  public func publishSignIn(user: User) {
    publisher.send(.signedIn(user))
  }

  public func publishSignOut() {
    publisher.send(.signedOut)
  }

  public func publishTokenRefresh(token: String) {
    publisher.send(.tokenRefreshed(token))
  }

  public func publishProfileUpdate(user: User) {
    publisher.send(.profileUpdated(user))
  }

  public func publishUserCreated(user: User) {
    publisher.send(.userCreated(user))
  }
}
