import CoreNFC

@available(iOS 15.0, *)
public class NFCReader: NSObject, ObservableObject, NFCNDEFReaderSessionDelegate {
  private let startAlert = String(localized: "Hold your iPhone near the tag")
  private let nfcReadSuccess = String(localized: "Successfully read NFC card")
  private let nfcReadEmptyCard = String(localized: "NFC card is empty")
  private let nfcReadError = String(localized: "NFC read error")
  private let nfcRecordParser = NFCRecordParser()
  @Published public var metadata: NFCPayload?
  public var session: NFCNDEFReaderSession?

  public func read() {
    guard NFCNDEFReaderSession.readingAvailable else {
      print("Error: NFC reading not available on this device.")
      return
    }
    // Configure and start the NFC reader session.
    session = NFCNDEFReaderSession(delegate: self, queue: nil, invalidateAfterFirstRead: true)
    session?.alertMessage = startAlert
    session?.begin()
  }

  public func readerSession(_ session: NFCNDEFReaderSession, didDetectNDEFs messages: [NFCNDEFMessage]) {
    DispatchQueue.main.async {
      guard let firstMessage = messages.first else { return }
      do {
        self.metadata = try self.nfcRecordParser.parseNFCMessage(firstMessage)
        session.alertMessage = self.nfcReadSuccess
      } catch NFCError.missingRecord {
        session.alertMessage = self.nfcReadEmptyCard
      } catch {
        session.alertMessage = self.nfcReadError
      }
    }
  }

  public func readerSessionDidBecomeActive(_: NFCNDEFReaderSession) { }

  // Callback when the session is invalidated (e.g., error, user cancel).
  public func readerSession(_: NFCNDEFReaderSession, didInvalidateWithError error: Error) {
    print("Session did invalidate with error: \(error)")
    session = nil
  }
}

public class NFCWriter: NSObject, ObservableObject, NFCNDEFReaderSessionDelegate {
  // Required delegate method to comply with protocol.
  public func readerSession(_: NFCNDEFReaderSession, didDetectNDEFs _: [NFCNDEFMessage]) { }

  private let startAlert = String(localized: "Hold your iPhone near the tag to write the record")
  private let wroteToTagSuccess = String(localized: "Successfully wrote to NFC tag")
  private let errorMoreThanOneTag = String(localized: "Detected more than 1 tag. Please try again.")
  private let endAlert = ""
  private var messages: [(String, String)] = []
  public var session: NFCNDEFReaderSession?

  enum PayloadType: String {
    case none = ""
    case text = "T"
    case url = "U"
  }

  public func write(_ messages: [(String, String)]) {
    guard NFCNDEFReaderSession.readingAvailable else {
      print("NFC is not available on this device.")
      return
    }
    self.messages = messages
    session = NFCNDEFReaderSession(delegate: self, queue: nil, invalidateAfterFirstRead: false)
    session?.alertMessage = startAlert
    session?.begin()
  }

  // Callback when NFC tags are detected during writing.
  public func readerSession(_ session: NFCNDEFReaderSession, didDetect tags: [NFCNDEFTag]) {
    if handleMultipleTags(tags, session: session) { return }
    guard let tag = tags.first else { return }
    connectToTag(tag, session: session)
  }

  private func handleMultipleTags(_ tags: [NFCNDEFTag], session: NFCNDEFReaderSession) -> Bool {
    if tags.count > 1 {
      session.alertMessage = errorMoreThanOneTag
      DispatchQueue.global().asyncAfter(deadline: .now() + .milliseconds(500)) {
        session.restartPolling()
      }
      return true
    }
    return false
  }

  private func connectToTag(_ tag: NFCNDEFTag, session: NFCNDEFReaderSession) {
    session.connect(to: tag) { error in
      if let error {
        self.invalidateSession(session, with: "Unable to connect to tag: \(error.localizedDescription)")
      } else {
        self.queryTagStatus(tag, session: session)
      }
    }
  }

  private func queryTagStatus(_ tag: NFCNDEFTag, session: NFCNDEFReaderSession) {
    tag.queryNDEFStatus { status, _, error in
      guard error == nil else {
        self.invalidateSession(session, with: "Unable to query the status of tag.")
        return
      }

      switch status {
      case .notSupported:
        self.invalidateSession(session, with: "Tag is not NDEF compliant.")
      case .readOnly:
        self.invalidateSession(session, with: "Read-only tag detected.")
      case .readWrite:
        self.writePayloadsToTag(tag, session: session)
      @unknown default:
        self.invalidateSession(session, with: "Unknown tag status.")
      }
    }
  }

  private func writePayloadsToTag(_ tag: NFCNDEFTag, session: NFCNDEFReaderSession) {
    let payloads = createPayloads()
    let message = NFCNDEFMessage(records: payloads)

    tag.writeNDEF(message) { error in
      if let error {
        self.invalidateSession(session, with: "Write to tag failed: \(error.localizedDescription)")
      } else {
        session.alertMessage = self.endAlert.isEmpty ? self.wroteToTagSuccess : self.endAlert
        session.invalidate()
      }
    }
  }

  private func createPayloads() -> [NFCNDEFPayload] {
    messages.compactMap { data, type in
      let payloadType = PayloadType(rawValue: type) ?? .none
      switch payloadType {
      case PayloadType.text:
        return NFCNDEFPayload(
          format: .nfcWellKnown,
          type: Data("T".utf8),
          identifier: Data(),
          payload: Data(data.utf8)
        )
      case PayloadType.url:
        return NFCNDEFPayload.wellKnownTypeURIPayload(string: data)
      default:
        return nil
      }
    }
  }

  // Invalidates the session with a user-friendly message.
  private func invalidateSession(_ session: NFCNDEFReaderSession, with message: String) {
    session.alertMessage = message
    session.invalidate()
  }

  // Callback when the reader session becomes active.
  public func readerSessionDidBecomeActive(_: NFCNDEFReaderSession) {
    // No actions required for now.
  }

  // Callback when the session is invalidated (e.g., error, user cancel).
  public func readerSession(_: NFCNDEFReaderSession, didInvalidateWithError error: Error) {
    print("Session did invalidate with error: \(error)")
    session = nil
  }
}
