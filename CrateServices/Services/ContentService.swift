import Foundation
import SwiftData

public protocol ContentServiceProtocol {
  // Content endpoints
  func getTrending() async throws -> [ContentDTO]
  func getLatest(start: Int, size: Int) async throws -> [ContentDTO]

  // Content deletion endpoints
  func deleteContent(id: Int) async throws -> Bool
  func deleteAllContent() async throws -> Bool

  // Unfurl endpoint
  func unfurl(url: String) async throws -> ContentDTO

  // Single recent content
  func getSingleRecent() async throws -> ContentDTO?

  // SwiftData operations
  func fetchAndSaveTrending(context: ModelContext) async throws -> [ContentDTO]
  func getAllTrendingContent(context: ModelContext) throws -> [Content]
  func deleteAllTrendingContent(context: ModelContext) throws
  func saveTrendingContent(_ content: [Content], context: ModelContext) throws

  // Recent content operations
  func saveRecentContent(_ content: [Content], context: ModelContext) throws
  func getContentByServerId(_ serverId: Int, context: ModelContext) throws -> Content?
}

public struct ContentService: ContentServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState

  public init(client: HTTPClientProtocol = HTTPClient(), userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState
  }

  // MARK: - Trending endpoint

  public func getTrending() async throws -> [ContentDTO] {
    struct TrendingParams: Codable {
      let start: Int
      let size: Int
    }

    let params = TrendingParams(start: 0, size: 20)
    let content: [ServerContentDTO] = try await client.get(
      path: "/api/v1/content/trending", parameters: params)

    return content.map { $0.toContentDTO() }
  }

  // MARK: - Latest endpoint

  public func getLatest(start: Int = 0, size: Int = 20) async throws -> [ContentDTO] {
    struct LatestParams: Codable {
      let start: Int
      let size: Int
    }

    let params = LatestParams(start: start, size: size)
    let content: [ServerContentDTO] = try await client.get(
      path: "/api/v1/content/latest", parameters: params)

    return content.map { $0.toContentDTO() }
  }

  // MARK: - SwiftData operations

  public func fetchAndSaveTrending(context: ModelContext) async throws -> [ContentDTO] {
    let content = try await getTrending()

    let models = content.map { dto in
      TrendingContent(
        serverId: dto.serverId,
        detail: dto.detail,
        title: dto.title,
        mediaUrl: dto.mediaUrl,
        url: dto.url,
        updatedAt: dto.updatedAt ?? Date(),
        createdAt: dto.createdAt ?? Date()
      )
    }

    try saveTrendingContentModels(models, context: context)
    return content
  }

  public func getAllTrendingContent(context: ModelContext) throws -> [Content] {
    var fetchDescriptor = FetchDescriptor<TrendingContent>()
    fetchDescriptor.sortBy = [SortDescriptor(\TrendingContent.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20
    let existingTrendingContent = try context.fetch(fetchDescriptor)

    let contentModels = existingTrendingContent.map { $0.toContent() }
    return contentModels.uniqued(on: { content in
      "\(content.title ?? ""):\(content.url ?? "")"
    })
  }

  public func getTrendingContentByServerId(_ serverId: Int, context: ModelContext) throws
  -> TrendingContent? {
    let predicate = #Predicate<TrendingContent> { content in
      content.serverId == serverId
    }

    let descriptor = FetchDescriptor<TrendingContent>(predicate: predicate)
    let content = try context.fetch(descriptor)
    return content.first
  }

  public func deleteAllTrendingContent(context: ModelContext) throws {
    try context.delete(model: TrendingContent.self)
    try context.save()
  }

  public func saveTrendingContent(_ content: [Content], context: ModelContext) throws {
    // Convert Content to TrendingContent and save
    let trendingContent = content.map { $0.toTrendingContent() }
    try saveTrendingContentModels(trendingContent, context: context)
  }

  private func saveTrendingContentModels(_ content: [TrendingContent], context: ModelContext) throws {
    // Check for existing content with the same serverId and update them instead of inserting duplicates
    for item in content {
      if let serverId = item.serverId,
         let existingContent = try? getTrendingContentByServerId(serverId, context: context) {
        // Update existing content properties
        existingContent.detail = item.detail
        existingContent.title = item.title
        existingContent.mediaUrl = item.mediaUrl
        existingContent.url = item.url
        existingContent.updatedAt = item.updatedAt
      } else {
        // Insert new content
        context.insert(item)
      }
    }

    try context.save()
  }

  // MARK: - Recent content operations

  public func saveRecentContent(_ content: [Content], context: ModelContext) throws {
    // Check for existing content with the same serverId and update them instead of inserting duplicates
    for item in content {
      if let serverId = item.serverId,
         let existingContent = try? getContentByServerId(serverId, context: context) {
        // Update existing content properties
        existingContent.detail = item.detail
        existingContent.title = item.title
        existingContent.mediaUrl = item.mediaUrl
        existingContent.url = item.url
        existingContent.updatedAt = item.updatedAt
      } else {
        // Insert new content
        context.insert(item)
      }
    }

    try context.save()
  }

  public func getContentByServerId(_ serverId: Int, context: ModelContext) throws -> Content? {
    let predicate = #Predicate<Content> { content in
      content.serverId == serverId
    }

    let descriptor = FetchDescriptor<Content>(predicate: predicate)
    let content = try context.fetch(descriptor)
    return content.first
  }

  // MARK: - Unfurl endpoint

  public func unfurl(url: String) async throws -> ContentDTO {
    struct UnfurlParams: Codable {
      let url: String
    }

    let params = UnfurlParams(url: url)

    if userState.isSignedIn {
      // Signed-in users: content gets saved with serverId
      let content: ServerContentDTO = try await client.post(
        path: "/api/v1/unfurl", body: params)
      return content.toContentDTO()
    } else {
      // Anonymous users: content returned but not saved (no serverId)
      let content: ServerContentDTO = try await client.post(
        path: "/api/v1/unfurl/anonymous", body: params)
      return content.toContentDTO()
    }
  }

  // MARK: - Single recent content

  public func getSingleRecent() async throws -> ContentDTO? {
    let content = try await getLatest(start: 0, size: 1)
    return content.first
  }

  // MARK: - Content Deletion endpoints

  /// Deletes a specific content item by ID for the user
  /// DELETE /api/v1/content/{id}
  public func deleteContent(id: Int) async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw ContentServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/content/\(id)", body: EmptyParameters())
      print("✅ Successfully deleted content with ID \(id) on the server")
      return true
    } catch {
      print("❌ Error deleting content with ID \(id): \(error.localizedDescription)")
      throw ContentServiceError.deleteContentFailed
    }
  }

  /// Deletes all content for the user
  /// DELETE /api/v1/content
  public func deleteAllContent() async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw ContentServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/content", body: EmptyParameters())
      print("✅ Successfully deleted all content on the server")
      return true
    } catch {
      print("❌ Error deleting all content: \(error.localizedDescription)")
      throw ContentServiceError.deleteAllContentFailed
    }
  }
}

// MARK: - Content Service Errors

public enum ContentServiceError: Error {
  case notAuthenticated
  case deleteContentFailed
  case deleteAllContentFailed
}

// Add this at the top of the file, after the imports
private struct EmptyParameters: Encodable {}
