import Foundation

extension Array {
  func uniqued<T: <PERSON>hab<PERSON>>(on uniqueKey: (Element) -> T) -> [Element] {
    var seen = Set<T>()
    return filter { seen.insert(uniqueKey($0)).inserted }
  }
}

extension J<PERSON>NDecoder {
  static func iso8601WithFractionalSeconds() -> JSONDecoder {
    let decoder = JSONDecoder()
    decoder.dateDecodingStrategy = .custom { decoder -> Date in
      let container = try decoder.singleValueContainer()
      let dateString = try container.decode(String.self)

      let formatter = ISO8601DateFormatter()
      formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

      guard let date = formatter.date(from: dateString) else {
        throw DecodingError.dataCorruptedError(
          in: container,
          debugDescription: "Invalid date format: \(dateString)"
        )
      }
      return date
    }
    return decoder
  }
}
