// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		25C0D7C42D309FFA00761251 /* Factory in Frameworks */ = {isa = PBXBuildFile; productRef = 25C0D7C32D309FFA00761251 /* Factory */; };
		934F1D752D2C51DA005A56FD /* CoreNFC.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 934F1D732D2C50C9005A56FD /* CoreNFC.framework */; };
		9370730A2D6E231600CBA639 /* SwiftSoup in Frameworks */ = {isa = PBXBuildFile; productRef = 937073092D6E231600CBA639 /* SwiftSoup */; };
		CFEABE692DCEC20B00C095A6 /* MSAL in Frameworks */ = {isa = PBXBuildFile; productRef = CFEABE682DCEC20B00C095A6 /* MSAL */; settings = {ATTRIBUTES = (Required, ); }; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		253C27012D2C387800E0DFEB /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93F722BA2D44642A001F4645 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		253C27032D2C387800E0DFEB /* libCrateServices.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libCrateServices.a; sourceTree = BUILT_PRODUCTS_DIR; };
		934F1D732D2C50C9005A56FD /* CoreNFC.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreNFC.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/iOSSupport/System/Library/Frameworks/CoreNFC.framework; sourceTree = DEVELOPER_DIR; };
		934F1D782D2C5410005A56FD /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/iOSSupport/System/Library/Frameworks/SwiftUI.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		253C27962D2C612900E0DFEB /* Models */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Models;
			sourceTree = "<group>";
		};
		253C27A72D2C612E00E0DFEB /* Utilities */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Utilities;
			sourceTree = "<group>";
		};
		25C0D7AC2D308D5800761251 /* Services */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Services;
			sourceTree = "<group>";
		};
		930E97172DDB8C18000BCC50 /* Actor */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Actor;
			sourceTree = "<group>";
		};
		939F9AD82D44784700C9E858 /* Client */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Client;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		253C27002D2C387800E0DFEB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				934F1D752D2C51DA005A56FD /* CoreNFC.framework in Frameworks */,
				CFEABE692DCEC20B00C095A6 /* MSAL in Frameworks */,
				25C0D7C42D309FFA00761251 /* Factory in Frameworks */,
				9370730A2D6E231600CBA639 /* SwiftSoup in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		253C26FA2D2C387800E0DFEB = {
			isa = PBXGroup;
			children = (
				930E97172DDB8C18000BCC50 /* Actor */,
				939F9AD82D44784700C9E858 /* Client */,
				25C0D7AC2D308D5800761251 /* Services */,
				253C27372D2C392600E0DFEB /* Frameworks */,
				253C27042D2C387800E0DFEB /* Products */,
				253C27962D2C612900E0DFEB /* Models */,
				253C27A72D2C612E00E0DFEB /* Utilities */,
			);
			sourceTree = "<group>";
		};
		253C27042D2C387800E0DFEB /* Products */ = {
			isa = PBXGroup;
			children = (
				253C27032D2C387800E0DFEB /* libCrateServices.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		253C27372D2C392600E0DFEB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				934F1D782D2C5410005A56FD /* SwiftUI.framework */,
				934F1D732D2C50C9005A56FD /* CoreNFC.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		253C27022D2C387800E0DFEB /* CrateServices */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 253C270A2D2C387800E0DFEB /* Build configuration list for PBXNativeTarget "CrateServices" */;
			buildPhases = (
				253C26FF2D2C387800E0DFEB /* Sources */,
				253C27002D2C387800E0DFEB /* Frameworks */,
				253C27012D2C387800E0DFEB /* CopyFiles */,
				93F722BA2D44642A001F4645 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				253C27962D2C612900E0DFEB /* Models */,
				253C27A72D2C612E00E0DFEB /* Utilities */,
				25C0D7AC2D308D5800761251 /* Services */,
				930E97172DDB8C18000BCC50 /* Actor */,
				939F9AD82D44784700C9E858 /* Client */,
			);
			name = CrateServices;
			packageProductDependencies = (
				25C0D7C32D309FFA00761251 /* Factory */,
				937073092D6E231600CBA639 /* SwiftSoup */,
				CFEABE682DCEC20B00C095A6 /* MSAL */,
			);
			productName = CrateServices;
			productReference = 253C27032D2C387800E0DFEB /* libCrateServices.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		253C26FB2D2C387800E0DFEB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					253C27022D2C387800E0DFEB = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 253C26FE2D2C387800E0DFEB /* Build configuration list for PBXProject "CrateServices" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 253C26FA2D2C387800E0DFEB;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				25C0D7C22D309FFA00761251 /* XCRemoteSwiftPackageReference "Factory" */,
				937073082D6E22F500CBA639 /* XCRemoteSwiftPackageReference "SwiftSoup" */,
				CFEABE672DCEC20B00C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 253C27042D2C387800E0DFEB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				253C27022D2C387800E0DFEB /* CrateServices */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		253C26FF2D2C387800E0DFEB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		253C27082D2C387800E0DFEB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Debug;
		};
		253C27092D2C387800E0DFEB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		253C270B2D2C387800E0DFEB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		253C270C2D2C387800E0DFEB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8VKJ385GDV;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		253C26FE2D2C387800E0DFEB /* Build configuration list for PBXProject "CrateServices" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				253C27082D2C387800E0DFEB /* Debug */,
				253C27092D2C387800E0DFEB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		253C270A2D2C387800E0DFEB /* Build configuration list for PBXNativeTarget "CrateServices" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				253C270B2D2C387800E0DFEB /* Debug */,
				253C270C2D2C387800E0DFEB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		25C0D7C22D309FFA00761251 /* XCRemoteSwiftPackageReference "Factory" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hmlongco/Factory";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.3;
			};
		};
		937073082D6E22F500CBA639 /* XCRemoteSwiftPackageReference "SwiftSoup" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/scinfu/SwiftSoup";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.8.0;
			};
		};
		CFEABE672DCEC20B00C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AzureAD/microsoft-authentication-library-for-objc";
			requirement = {
				kind = upToNextMinorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		25C0D7C32D309FFA00761251 /* Factory */ = {
			isa = XCSwiftPackageProductDependency;
			package = 25C0D7C22D309FFA00761251 /* XCRemoteSwiftPackageReference "Factory" */;
			productName = Factory;
		};
		937073092D6E231600CBA639 /* SwiftSoup */ = {
			isa = XCSwiftPackageProductDependency;
			package = 937073082D6E22F500CBA639 /* XCRemoteSwiftPackageReference "SwiftSoup" */;
			productName = SwiftSoup;
		};
		CFEABE682DCEC20B00C095A6 /* MSAL */ = {
			isa = XCSwiftPackageProductDependency;
			package = CFEABE672DCEC20B00C095A6 /* XCRemoteSwiftPackageReference "microsoft-authentication-library-for-objc" */;
			productName = MSAL;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 253C26FB2D2C387800E0DFEB /* Project object */;
}
